#!/usr/bin/env python3
"""
Diagnostic pour identifier pourquoi certaines tâches sont filtrées lors de l'analyse.

Ce script analyse les 401 tâches trouvées initialement et identifie lesquelles
sont exclues à chaque étape du processus de filtrage.
"""

import json
import os
from pathlib import Path
from collections import defaultdict, Counter
from typing import Dict, List, Any, Tuple, Optional
import logging

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class TaskFilteringDiagnostic:
    """Diagnostic du filtrage des tâches."""
    
    def __init__(self, scenarios_dir: str, results_dir: str):
        self.scenarios_dir = Path(scenarios_dir)
        self.results_dir = Path(results_dir)
        self.filtering_stats = {
            'total_found': 0,
            'scenario_missing': 0,
            'scenario_load_error': 0,
            'empty_commands': 0,
            'characteristics_missing': 0,
            'successfully_processed': 0
        }
        self.excluded_tasks = defaultdict(list)
        
    def diagnose_filtering(self):
        """Diagnostique le processus de filtrage."""
        logger.info("Début du diagnostic de filtrage...")
        
        # Obtenir la liste complète des tâches depuis les scénarios
        all_scenario_files = list(self.scenarios_dir.glob("*_TEST0_REDUCED.agi"))
        task_ids = set()
        
        for file_path in all_scenario_files:
            task_id = file_path.stem.replace('_TEST0_REDUCED', '')
            task_ids.add(task_id)
        
        self.filtering_stats['total_found'] = len(task_ids)
        logger.info(f"Total des tâches trouvées: {len(task_ids)}")
        
        # Analyser chaque tâche
        for task_id in sorted(task_ids):
            self.analyze_single_task(task_id)
        
        # Générer le rapport
        self.generate_diagnostic_report()
    
    def analyze_single_task(self, task_id: str):
        """Analyse une tâche individuelle pour identifier les points de filtrage."""
        # Étape 1: Vérifier l'existence du scénario
        scenario_path = self.scenarios_dir / f"{task_id}_TEST0_REDUCED.agi"
        if not scenario_path.exists():
            self.filtering_stats['scenario_missing'] += 1
            self.excluded_tasks['scenario_missing'].append(task_id)
            return
        
        # Étape 2: Tenter de charger le scénario
        try:
            with open(scenario_path, 'r', encoding='utf-8') as f:
                scenario = f.read().strip()
        except Exception as e:
            self.filtering_stats['scenario_load_error'] += 1
            self.excluded_tasks['scenario_load_error'].append((task_id, str(e)))
            return
        
        # Étape 3: Extraire les commandes
        enhanced_commands = self.extract_enhanced_commands_from_scenario(scenario)
        if not enhanced_commands:
            self.filtering_stats['empty_commands'] += 1
            self.excluded_tasks['empty_commands'].append((task_id, scenario[:100]))
            return
        
        # Étape 4: Vérifier les caractéristiques
        characteristics = self.load_task_characteristics(task_id)
        if not characteristics:
            self.filtering_stats['characteristics_missing'] += 1
            self.excluded_tasks['characteristics_missing'].append(task_id)
            return
        
        # Tâche traitée avec succès
        self.filtering_stats['successfully_processed'] += 1
    
    def extract_enhanced_commands_from_scenario(self, scenario: str) -> List[str]:
        """Extrait les commandes avec la classification améliorée (version simplifiée)."""
        if not scenario:
            return []
        
        commands = []
        lines = scenario.split('\n')
        
        for line in lines:
            line = line.strip()
            if not line:
                continue
            
            # Traitement spécial pour MOTIF
            if line.startswith('MOTIF'):
                classified_command = self.classify_motif_command(line)
                commands.append(classified_command)
            
            # Autres commandes
            elif line.startswith('RESIZE'):
                commands.append('RESIZE')
            elif line.startswith('TRANSFERT'):
                commands.append('TRANSFERT')
            elif line.startswith('FILL'):
                commands.append('FILL')
            elif line.startswith('EDIT'):
                commands.append('EDIT')
            elif line.startswith('EDITS'):
                commands.append('EDITS')
            elif line.startswith('REPLACE'):
                commands.append('REPLACE')
            elif line.startswith('EXTRACT'):
                commands.append('EXTRACT')
        
        return commands
    
    def classify_motif_command(self, motif_content: str) -> str:
        """Classification simplifiée des commandes MOTIF."""
        # Vérifier les composants de base
        has_cut = 'CUT' in motif_content
        has_copy = 'COPY' in motif_content
        has_paste = 'PASTE' in motif_content
        has_color = 'COLOR' in motif_content
        
        # Détecter les transformations
        has_transformations = any(transform in motif_content for transform in 
                                ['ROTATE', 'FLIP', 'DIVIDE', 'MULTIPLY', 'SCALE', 'MIRROR'])
        
        # Appliquer les règles de classification
        has_basic_pattern = (has_cut or has_copy) and has_paste
        
        if not has_basic_pattern:
            return 'MOTIF'
        
        if not has_transformations and not has_color:
            return 'MOVE'
        
        if not has_transformations and has_color:
            return 'MOVE MOTIF'
        
        if has_transformations:
            if has_color:
                return 'TRANSFORMATION MOTIF'
            else:
                return 'TRANSFORMATION'
        
        return 'MOTIF'
    
    def load_task_characteristics(self, task_id: str) -> Dict[str, Any]:
        """Charge les caractéristiques d'une tâche."""
        characteristics = {}
        
        file_types = [
            'io_differences',
            'input_differences', 
            'output_differences',
            'test0_input_params',
            'train0_input_params',
            'train1_input_params',
            'train2_input_params'
        ]
        
        for file_type in file_types:
            file_path = self.results_dir / f"{task_id}_{file_type}.json"
            if file_path.exists():
                try:
                    with open(file_path, 'r', encoding='utf-8') as f:
                        characteristics[file_type] = json.load(f)
                except Exception as e:
                    logger.debug(f"Erreur lors du chargement de {file_path}: {e}")
        
        return characteristics
    
    def generate_diagnostic_report(self):
        """Génère un rapport de diagnostic."""
        logger.info("Génération du rapport de diagnostic...")
        
        # Afficher les statistiques
        print("\n" + "="*60)
        print("DIAGNOSTIC DU FILTRAGE DES TÂCHES")
        print("="*60)
        
        total = self.filtering_stats['total_found']
        print(f"📊 Total des tâches trouvées: {total}")
        print(f"✅ Tâches traitées avec succès: {self.filtering_stats['successfully_processed']}")
        print(f"❌ Tâches exclues: {total - self.filtering_stats['successfully_processed']}")
        
        print("\n🔍 Raisons d'exclusion:")
        print(f"   • Scénario manquant: {self.filtering_stats['scenario_missing']}")
        print(f"   • Erreur de chargement: {self.filtering_stats['scenario_load_error']}")
        print(f"   • Commandes vides: {self.filtering_stats['empty_commands']}")
        print(f"   • Caractéristiques manquantes: {self.filtering_stats['characteristics_missing']}")
        
        # Détails des exclusions
        print("\n📋 Détails des exclusions:")
        
        if self.excluded_tasks['scenario_missing']:
            print(f"\n   Scénarios manquants ({len(self.excluded_tasks['scenario_missing'])}):")
            for task_id in self.excluded_tasks['scenario_missing'][:5]:
                print(f"     - {task_id}")
            if len(self.excluded_tasks['scenario_missing']) > 5:
                print(f"     ... et {len(self.excluded_tasks['scenario_missing']) - 5} autres")
        
        if self.excluded_tasks['scenario_load_error']:
            print(f"\n   Erreurs de chargement ({len(self.excluded_tasks['scenario_load_error'])}):")
            for task_id, error in self.excluded_tasks['scenario_load_error'][:3]:
                print(f"     - {task_id}: {error}")
        
        if self.excluded_tasks['empty_commands']:
            print(f"\n   Commandes vides ({len(self.excluded_tasks['empty_commands'])}):")
            for task_id, scenario_preview in self.excluded_tasks['empty_commands'][:3]:
                print(f"     - {task_id}: {scenario_preview}...")
        
        if self.excluded_tasks['characteristics_missing']:
            print(f"\n   Caractéristiques manquantes ({len(self.excluded_tasks['characteristics_missing'])}):")
            for task_id in self.excluded_tasks['characteristics_missing'][:5]:
                print(f"     - {task_id}")
        
        print("="*60)
        
        # Sauvegarder le rapport détaillé
        self.save_detailed_report()
    
    def save_detailed_report(self):
        """Sauvegarde un rapport détaillé."""
        output_path = Path("src/grid_analysis/task_filtering_diagnostic.json")
        
        report = {
            'filtering_statistics': self.filtering_stats,
            'excluded_tasks_details': {
                'scenario_missing': self.excluded_tasks['scenario_missing'],
                'scenario_load_error': [{'task_id': task_id, 'error': error} 
                                       for task_id, error in self.excluded_tasks['scenario_load_error']],
                'empty_commands': [{'task_id': task_id, 'scenario_preview': preview} 
                                  for task_id, preview in self.excluded_tasks['empty_commands']],
                'characteristics_missing': self.excluded_tasks['characteristics_missing']
            },
            'summary': {
                'total_found': self.filtering_stats['total_found'],
                'successfully_processed': self.filtering_stats['successfully_processed'],
                'exclusion_rate': ((self.filtering_stats['total_found'] - self.filtering_stats['successfully_processed']) / self.filtering_stats['total_found']) * 100
            }
        }
        
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(report, f, indent=2, ensure_ascii=False)
        
        logger.info(f"Rapport détaillé sauvegardé dans {output_path}")
    
    def analyze_specific_exclusions(self):
        """Analyse spécifique des tâches exclues pour commandes vides."""
        print("\n🔍 Analyse des scénarios avec commandes vides:")
        
        for task_id, scenario_preview in self.excluded_tasks['empty_commands'][:10]:
            scenario_path = self.scenarios_dir / f"{task_id}_TEST0_REDUCED.agi"
            if scenario_path.exists():
                try:
                    with open(scenario_path, 'r', encoding='utf-8') as f:
                        full_scenario = f.read().strip()
                    print(f"\n   {task_id}:")
                    print(f"     Contenu: {full_scenario}")
                    
                    # Analyser pourquoi aucune commande n'est extraite
                    lines = full_scenario.split('\n')
                    print(f"     Lignes: {len(lines)}")
                    for i, line in enumerate(lines):
                        line = line.strip()
                        if line:
                            print(f"       {i}: '{line}'")
                            
                except Exception as e:
                    print(f"   {task_id}: Erreur de lecture - {e}")

def main():
    """Fonction principale."""
    scenarios_dir = "src/grid_analysis/scenarios/training/reduced"
    results_dir = "src/grid_analysis/results/training"
    
    if not Path(scenarios_dir).exists():
        logger.error(f"Répertoire des scénarios non trouvé: {scenarios_dir}")
        return
    
    if not Path(results_dir).exists():
        logger.error(f"Répertoire des résultats non trouvé: {results_dir}")
        return
    
    # Créer le diagnostic et l'exécuter
    diagnostic = TaskFilteringDiagnostic(scenarios_dir, results_dir)
    diagnostic.diagnose_filtering()
    
    # Analyse spécifique des exclusions
    diagnostic.analyze_specific_exclusions()
    
    logger.info("Diagnostic terminé!")

if __name__ == "__main__":
    main()