"""
Analyse des différences entre les grilles OUTPUT des exemples train.
Hiérarchie : <PERSON><PERSON>t → <PERSON>ly<PERSON> → Notable
"""

import numpy as np
from typing import Dict, List, Any, Tuple
from collections import Counter
try:
    from .grid_parameters import GridParameters
except ImportError:
    # Import direct si exécuté comme script
    from grid_parameters import GridParameters


class OutputDifferences:
    """
    Analyse des différences entre les grilles OUTPUT des exemples train.
    
    Niveau 1 : Différences brutes (calculs directs)
    Niveau 2 : Analyses (patterns, tendances)  
    Niveau 3 : Notable (ce qui ressort, insights)
    """
    
    def __init__(self, output_grids: List[np.ndarray]):
        """Initialise avec la liste des grilles output des exemples train."""
        self.output_grids = output_grids
        self.grid_params = [GridParameters(grid) for grid in output_grids]
        
        # Calculs des 3 niveaux
        self.raw_differences = self._compute_raw_differences()
        self.analysis = self._compute_analysis()
        self.notable = self._compute_notable()
    
    def _compute_raw_differences(self) -> Dict[str, Any]:
        """Niveau 1 : Différences brutes entre les outputs."""
        if len(self.output_grids) < 2:
            return {"error": "Moins de 2 grilles output pour comparer"}
        
        raw = {
            "grid_count": len(self.output_grids),
            "dimension_variations": self._get_dimension_variations(),
            "color_variations": self._get_color_variations(),
            "size_variations": self._get_size_variations(),
            "density_variations": self._get_density_variations(),
            "structural_variations": self._get_structural_variations(),
            "pairwise_differences": self._get_pairwise_differences()
        }
        
        return raw
    
    def _get_dimension_variations(self) -> Dict[str, Any]:
        """Variations dimensionnelles entre outputs."""
        dimensions = [(grid.shape[1], grid.shape[0]) for grid in self.output_grids]  # (width, height)
        
        return {
            "all_dimensions": dimensions,
            "unique_dimensions": list(set(dimensions)),
            "dimension_count": len(set(dimensions)),
            "same_dimensions": len(set(dimensions)) == 1,
            "min_size": min(w * h for w, h in dimensions),
            "max_size": max(w * h for w, h in dimensions),
            "size_range": max(w * h for w, h in dimensions) - min(w * h for w, h in dimensions),
            "aspect_ratios": [w / h if h != 0 else float('inf') for w, h in dimensions]
        }
    
    def _get_color_variations(self) -> Dict[str, Any]:
        """Variations chromatiques entre outputs."""
        all_colors = set()
        color_counts = []
        dominant_colors = []
        standout_patterns = []
        
        for params in self.grid_params:
            all_colors.update(params.color_palette)
            color_counts.append(params.unique_colors_count)
            dominant_colors.append(params.dominant_color)
            standout_patterns.append(params.has_standout_color)
        
        return {
            "global_palette": sorted(list(all_colors)),
            "global_palette_size": len(all_colors),
            "color_counts_per_grid": color_counts,
            "min_colors": min(color_counts),
            "max_colors": max(color_counts),
            "color_count_range": max(color_counts) - min(color_counts),
            "dominant_colors": dominant_colors,
            "same_dominant_color": len(set(dominant_colors)) == 1,
            "dominant_color_distribution": dict(Counter(dominant_colors)),
            "standout_patterns": standout_patterns,
            "consistent_standout": len(set(standout_patterns)) == 1
        }
    
    def _get_size_variations(self) -> Dict[str, Any]:
        """Variations de taille entre outputs."""
        total_cells = [params.total_cells for params in self.grid_params]
        
        return {
            "total_cells_per_grid": total_cells,
            "min_total_cells": min(total_cells),
            "max_total_cells": max(total_cells),
            "total_cells_range": max(total_cells) - min(total_cells),
            "same_total_cells": len(set(total_cells)) == 1,
            "size_ratios": self._calculate_size_ratios(total_cells)
        }
    
    def _calculate_size_ratios(self, sizes: List[int]) -> Dict[str, Any]:
        """Calcule les ratios de taille entre outputs."""
        if len(sizes) < 2:
            return {"ratios": [], "consistent_ratio": False}
        
        ratios = []
        for i in range(1, len(sizes)):
            if sizes[i-1] != 0:
                ratios.append(sizes[i] / sizes[i-1])
        
        return {
            "ratios": ratios,
            "consistent_ratio": len(set(ratios)) == 1 if ratios else False,
            "common_ratio": ratios[0] if ratios and len(set(ratios)) == 1 else None
        }
    
    def _get_density_variations(self) -> Dict[str, Any]:
        """Variations de densité colorée entre outputs."""
        densities = [params.colored_density for params in self.grid_params]
        
        return {
            "densities": densities,
            "min_density": min(densities),
            "max_density": max(densities),
            "density_range": max(densities) - min(densities),
            "average_density": sum(densities) / len(densities),
            "density_trend": self._get_trend(densities)
        }
    
    def _get_structural_variations(self) -> Dict[str, Any]:
        """Variations structurelles spécifiques aux outputs."""
        return {
            "complexity_scores": self._calculate_complexity_scores(),
            "regularity_scores": self._calculate_regularity_scores(),
            "fill_patterns": self._analyze_fill_patterns()
        }
    
    def _calculate_complexity_scores(self) -> List[float]:
        """Calcule des scores de complexité pour chaque output."""
        scores = []
        for params in self.grid_params:
            # Complexité = (nombre de couleurs × densité) / taille normalisée
            normalized_size = params.total_cells / 100.0  # Normalisation
            complexity = (params.unique_colors_count * params.colored_density) / max(0.1, normalized_size)
            scores.append(complexity)
        return scores
    
    def _calculate_regularity_scores(self) -> List[float]:
        """Calcule des scores de régularité pour chaque output."""
        scores = []
        for params in self.grid_params:
            # Régularité basée sur la distribution des couleurs
            if params.unique_colors_count <= 1:
                scores.append(1.0)  # Très régulier (une seule couleur)
            else:
                # Entropie normalisée de la distribution des couleurs
                total = params.total_cells
                entropy = 0.0
                for count in params.color_distribution.values():
                    if count > 0:
                        p = count / total
                        entropy -= p * np.log2(p)
                
                max_entropy = np.log2(params.unique_colors_count)
                regularity = 1.0 - (entropy / max_entropy if max_entropy > 0 else 0)
                scores.append(regularity)
        
        return scores
    
    def _analyze_fill_patterns(self) -> Dict[str, Any]:
        """Analyse les patterns de remplissage dans les outputs."""
        fill_analysis = {
            "sparse_grids": [],  # Grilles avec beaucoup de fond
            "dense_grids": [],   # Grilles très remplies
            "balanced_grids": [] # Grilles équilibrées
        }
        
        for i, params in enumerate(self.grid_params):
            if params.colored_density < 0.3:
                fill_analysis["sparse_grids"].append(i)
            elif params.colored_density > 0.7:
                fill_analysis["dense_grids"].append(i)
            else:
                fill_analysis["balanced_grids"].append(i)
        
        return fill_analysis
    
    def _get_pairwise_differences(self) -> List[Dict[str, Any]]:
        """Différences par paires entre outputs."""
        differences = []
        
        for i in range(len(self.output_grids)):
            for j in range(i + 1, len(self.output_grids)):
                grid1, grid2 = self.output_grids[i], self.output_grids[j]
                params1, params2 = self.grid_params[i], self.grid_params[j]
                
                diff = {
                    "pair": (i, j),
                    "dimension_change": (grid2.shape[1] - grid1.shape[1], grid2.shape[0] - grid1.shape[0]),
                    "size_change": params2.total_cells - params1.total_cells,
                    "color_count_change": params2.unique_colors_count - params1.unique_colors_count,
                    "density_change": params2.colored_density - params1.colored_density,
                    "dominant_color_change": params1.dominant_color != params2.dominant_color,
                    "shared_colors": len(set(params1.color_palette) & set(params2.color_palette)),
                    "unique_to_first": list(set(params1.color_palette) - set(params2.color_palette)),
                    "unique_to_second": list(set(params2.color_palette) - set(params1.color_palette)),
                    "structural_similarity": self._calculate_structural_similarity(params1, params2)
                }
                
                differences.append(diff)
        
        return differences
    
    def _calculate_structural_similarity(self, params1: GridParameters, params2: GridParameters) -> float:
        """Calcule la similarité structurelle entre deux grilles."""
        # Similarité basée sur plusieurs facteurs
        factors = []
        
        # Similarité de taille
        size_sim = 1.0 - abs(params1.total_cells - params2.total_cells) / max(params1.total_cells, params2.total_cells)
        factors.append(size_sim)
        
        # Similarité de palette
        common_colors = len(set(params1.color_palette) & set(params2.color_palette))
        total_colors = len(set(params1.color_palette) | set(params2.color_palette))
        color_sim = common_colors / total_colors if total_colors > 0 else 1.0
        factors.append(color_sim)
        
        # Similarité de densité
        density_sim = 1.0 - abs(params1.colored_density - params2.colored_density)
        factors.append(density_sim)
        
        return sum(factors) / len(factors)
    
    def _get_trend(self, values: List[float]) -> str:
        """Détermine la tendance d'une série de valeurs."""
        if len(values) < 2:
            return "insufficient_data"
        
        diffs = [values[i+1] - values[i] for i in range(len(values)-1)]
        
        if all(d > 0 for d in diffs):
            return "increasing"
        elif all(d < 0 for d in diffs):
            return "decreasing"
        elif all(abs(d) < 0.01 for d in diffs):  # Tolérance pour les flottants
            return "constant"
        else:
            return "variable"
    
    def _compute_analysis(self) -> Dict[str, Any]:
        """Niveau 2 : Analyse des patterns dans les outputs."""
        raw = self.raw_differences
        
        if "error" in raw:
            return {"error": raw["error"]}
        
        analysis = {
            "dimension_patterns": self._analyze_dimension_patterns(),
            "color_patterns": self._analyze_color_patterns(),
            "structural_patterns": self._analyze_structural_patterns(),
            "progression_patterns": self._analyze_progression_patterns(),
            "consistency_analysis": self._analyze_consistency(),
            "output_characteristics": self._identify_output_characteristics()
        }
        
        return analysis
    
    def _analyze_dimension_patterns(self) -> Dict[str, Any]:
        """Analyse des patterns dimensionnels dans les outputs."""
        dim_vars = self.raw_differences["dimension_variations"]
        
        patterns = {
            "is_uniform_dimensions": dim_vars["same_dimensions"],
            "dimension_progression": None,
            "aspect_ratio_consistency": self._check_aspect_ratio_consistency(dim_vars["aspect_ratios"])
        }
        
        if len(dim_vars["all_dimensions"]) > 2:
            # Analyser la progression des dimensions
            widths = [d[0] for d in dim_vars["all_dimensions"]]
            heights = [d[1] for d in dim_vars["all_dimensions"]]
            
            width_diffs = [widths[i+1] - widths[i] for i in range(len(widths)-1)]
            height_diffs = [heights[i+1] - heights[i] for i in range(len(heights)-1)]
            
            patterns["dimension_progression"] = {
                "width_arithmetic": len(set(width_diffs)) == 1 if width_diffs else False,
                "height_arithmetic": len(set(height_diffs)) == 1 if height_diffs else False,
                "width_step": width_diffs[0] if width_diffs and len(set(width_diffs)) == 1 else None,
                "height_step": height_diffs[0] if height_diffs and len(set(height_diffs)) == 1 else None,
                "proportional_scaling": self._check_proportional_scaling(widths, heights)
            }
        
        return patterns
    
    def _check_aspect_ratio_consistency(self, ratios: List[float]) -> Dict[str, Any]:
        """Vérifie la cohérence des ratios d'aspect."""
        finite_ratios = [r for r in ratios if not np.isinf(r)]
        
        if not finite_ratios:
            return {"consistent": False, "pattern": "undefined"}
        
        unique_ratios = set(round(r, 2) for r in finite_ratios)  # Arrondi pour tolérance
        
        return {
            "consistent": len(unique_ratios) == 1,
            "pattern": "uniform" if len(unique_ratios) == 1 else "variable",
            "common_ratio": list(unique_ratios)[0] if len(unique_ratios) == 1 else None
        }
    
    def _check_proportional_scaling(self, widths: List[int], heights: List[int]) -> bool:
        """Vérifie si les dimensions évoluent proportionnellement."""
        if len(widths) < 2 or len(heights) < 2:
            return False
        
        width_ratios = [widths[i+1] / widths[i] for i in range(len(widths)-1) if widths[i] != 0]
        height_ratios = [heights[i+1] / heights[i] for i in range(len(heights)-1) if heights[i] != 0]
        
        if not width_ratios or not height_ratios:
            return False
        
        # Vérifier si les ratios sont similaires (tolérance de 5%)
        return all(abs(wr - hr) < 0.05 for wr, hr in zip(width_ratios, height_ratios))
    
    def _analyze_color_patterns(self) -> Dict[str, Any]:
        """Analyse des patterns chromatiques dans les outputs."""
        color_vars = self.raw_differences["color_variations"]
        
        patterns = {
            "uniform_palette_size": len(set(color_vars["color_counts_per_grid"])) == 1,
            "palette_progression": None,
            "dominant_color_stability": color_vars["same_dominant_color"],
            "standout_consistency": color_vars["consistent_standout"],
            "color_richness_trend": self._analyze_color_richness_trend(),
            "palette_evolution": self._analyze_palette_evolution()
        }
        
        # Progression du nombre de couleurs
        counts = color_vars["color_counts_per_grid"]
        if len(counts) > 2:
            count_diffs = [counts[i+1] - counts[i] for i in range(len(counts)-1)]
            patterns["palette_progression"] = {
                "arithmetic": len(set(count_diffs)) == 1 if count_diffs else False,
                "step": count_diffs[0] if count_diffs and len(set(count_diffs)) == 1 else None,
                "monotonic_increase": all(d >= 0 for d in count_diffs),
                "monotonic_decrease": all(d <= 0 for d in count_diffs)
            }
        
        return patterns
    
    def _analyze_color_richness_trend(self) -> Dict[str, Any]:
        """Analyse la tendance de richesse chromatique."""
        richness_scores = []
        for params in self.grid_params:
            # Richesse = nombre de couleurs / taille normalisée
            normalized_size = max(1, params.total_cells / 10)
            richness = params.unique_colors_count / normalized_size
            richness_scores.append(richness)
        
        return {
            "scores": richness_scores,
            "trend": self._get_trend(richness_scores),
            "range": max(richness_scores) - min(richness_scores) if richness_scores else 0
        }
    
    def _analyze_palette_evolution(self) -> Dict[str, Any]:
        """Analyse l'évolution de la palette entre outputs."""
        evolution = {
            "new_colors_per_step": [],
            "lost_colors_per_step": [],
            "cumulative_palette": []
        }
        
        if len(self.grid_params) < 2:
            return evolution
        
        for i in range(1, len(self.grid_params)):
            prev_colors = set(self.grid_params[i-1].color_palette)
            curr_colors = set(self.grid_params[i].color_palette)
            
            new_colors = curr_colors - prev_colors
            lost_colors = prev_colors - curr_colors
            
            evolution["new_colors_per_step"].append(list(new_colors))
            evolution["lost_colors_per_step"].append(list(lost_colors))
        
        # Palette cumulative
        seen_colors = set()
        for params in self.grid_params:
            seen_colors.update(params.color_palette)
            evolution["cumulative_palette"].append(list(seen_colors))
        
        return evolution
    
    def _analyze_structural_patterns(self) -> Dict[str, Any]:
        """Analyse des patterns structurels dans les outputs."""
        structural = self.raw_differences["structural_variations"]
        
        patterns = {
            "complexity_trend": self._get_trend(structural["complexity_scores"]),
            "regularity_trend": self._get_trend(structural["regularity_scores"]),
            "fill_pattern_analysis": self._analyze_fill_pattern_trends(structural["fill_patterns"]),
            "structural_consistency": self._analyze_structural_consistency()
        }
        
        return patterns
    
    def _analyze_fill_pattern_trends(self, fill_patterns: Dict[str, List[int]]) -> Dict[str, Any]:
        """Analyse les tendances de remplissage."""
        total_grids = len(self.output_grids)
        
        return {
            "sparse_ratio": len(fill_patterns["sparse_grids"]) / total_grids,
            "dense_ratio": len(fill_patterns["dense_grids"]) / total_grids,
            "balanced_ratio": len(fill_patterns["balanced_grids"]) / total_grids,
            "dominant_pattern": max(fill_patterns.keys(), key=lambda k: len(fill_patterns[k]))
        }
    
    def _analyze_structural_consistency(self) -> Dict[str, Any]:
        """Analyse la cohérence structurelle des outputs."""
        pairwise = self.raw_differences["pairwise_differences"]
        
        if not pairwise:
            return {"score": 0.0, "pattern": "insufficient_data"}
        
        similarities = [pair["structural_similarity"] for pair in pairwise]
        avg_similarity = sum(similarities) / len(similarities)
        
        return {
            "score": avg_similarity,
            "pattern": "high" if avg_similarity > 0.7 else "medium" if avg_similarity > 0.4 else "low",
            "similarities": similarities
        }
    
    def _analyze_progression_patterns(self) -> Dict[str, Any]:
        """Analyse des progressions générales dans les outputs."""
        densities = [params.colored_density for params in self.grid_params]
        sizes = [params.total_cells for params in self.grid_params]
        
        patterns = {
            "density_trend": self._get_trend(densities),
            "size_trend": self._get_trend(sizes),
            "complexity_trend": self._analyze_complexity_trend(),
            "output_evolution": self._analyze_output_evolution()
        }
        
        return patterns
    
    def _analyze_complexity_trend(self) -> Dict[str, Any]:
        """Analyse la tendance de complexité des outputs."""
        complexities = [params.unique_colors_count * params.total_cells for params in self.grid_params]
        
        return {
            "values": complexities,
            "trend": self._get_trend(complexities),
            "range": max(complexities) - min(complexities) if complexities else 0,
            "growth_rate": self._calculate_growth_rate(complexities)
        }
    
    def _calculate_growth_rate(self, values: List[float]) -> float:
        """Calcule le taux de croissance moyen."""
        if len(values) < 2:
            return 0.0
        
        rates = []
        for i in range(1, len(values)):
            if values[i-1] != 0:
                rate = (values[i] - values[i-1]) / values[i-1]
                rates.append(rate)
        
        return sum(rates) / len(rates) if rates else 0.0
    
    def _analyze_output_evolution(self) -> Dict[str, Any]:
        """Analyse l'évolution générale des outputs."""
        return {
            "becomes_more_complex": self._get_trend([params.unique_colors_count for params in self.grid_params]) == "increasing",
            "becomes_larger": self._get_trend([params.total_cells for params in self.grid_params]) == "increasing",
            "becomes_denser": self._get_trend([params.colored_density for params in self.grid_params]) == "increasing",
            "evolution_summary": self._summarize_evolution()
        }
    
    def _summarize_evolution(self) -> str:
        """Résume l'évolution des outputs en une phrase."""
        size_trend = self._get_trend([params.total_cells for params in self.grid_params])
        color_trend = self._get_trend([params.unique_colors_count for params in self.grid_params])
        density_trend = self._get_trend([params.colored_density for params in self.grid_params])
        
        trends = [size_trend, color_trend, density_trend]
        
        if all(t == "increasing" for t in trends):
            return "outputs_become_more_complex_overall"
        elif all(t == "decreasing" for t in trends):
            return "outputs_become_simpler_overall"
        elif all(t == "constant" for t in trends):
            return "outputs_remain_stable"
        else:
            return "outputs_have_mixed_evolution"
    
    def _analyze_consistency(self) -> Dict[str, Any]:
        """Analyse la cohérence entre les outputs."""
        pairwise = self.raw_differences["pairwise_differences"]
        
        consistency = {
            "dimension_consistency": self._check_dimension_consistency(pairwise),
            "color_consistency": self._check_color_consistency(pairwise),
            "structural_consistency": self._analyze_structural_consistency(),
            "overall_consistency_score": 0.0
        }
        
        # Score de cohérence global
        scores = []
        if consistency["dimension_consistency"]["score"] is not None:
            scores.append(consistency["dimension_consistency"]["score"])
        if consistency["color_consistency"]["score"] is not None:
            scores.append(consistency["color_consistency"]["score"])
        if consistency["structural_consistency"]["score"] is not None:
            scores.append(consistency["structural_consistency"]["score"])
        
        consistency["overall_consistency_score"] = sum(scores) / len(scores) if scores else 0.0
        
        return consistency
    
    def _check_dimension_consistency(self, pairwise: List[Dict]) -> Dict[str, Any]:
        """Vérifie la cohérence dimensionnelle des outputs."""
        if not pairwise:
            return {"score": None, "pattern": "no_pairs"}
        
        dimension_changes = [pair["dimension_change"] for pair in pairwise]
        unique_changes = set(dimension_changes)
        
        return {
            "score": 1.0 if len(unique_changes) == 1 else 0.5 if len(unique_changes) <= len(pairwise) // 2 else 0.0,
            "pattern": "uniform" if len(unique_changes) == 1 else "variable",
            "unique_changes": list(unique_changes)
        }
    
    def _check_color_consistency(self, pairwise: List[Dict]) -> Dict[str, Any]:
        """Vérifie la cohérence chromatique des outputs."""
        if not pairwise:
            return {"score": None, "pattern": "no_pairs"}
        
        color_changes = [pair["color_count_change"] for pair in pairwise]
        unique_changes = set(color_changes)
        
        return {
            "score": 1.0 if len(unique_changes) == 1 else 0.5 if len(unique_changes) <= len(pairwise) // 2 else 0.0,
            "pattern": "uniform" if len(unique_changes) == 1 else "variable",
            "unique_changes": list(unique_changes)
        }
    
    def _compute_notable(self) -> Dict[str, Any]:
        """Niveau 3 : Ce qui est notable dans les outputs."""
        if "error" in self.raw_differences:
            return {"error": self.raw_differences["error"]}
        
        notable = {
            "key_insights": [],
            "anomalies": [],
            "strong_patterns": [],
            "output_characteristics": [],
            "recommendations": [],
            "confidence_score": 0.0
        }
        
        notable["key_insights"] = self._identify_key_insights()
        notable["anomalies"] = self._identify_anomalies()
        notable["strong_patterns"] = self._identify_strong_patterns()
        notable["output_characteristics"] = self._identify_output_characteristics()
        notable["recommendations"] = self._generate_recommendations()
        notable["confidence_score"] = self._calculate_confidence_score()
        
        return notable
    
    def _identify_key_insights(self) -> List[str]:
        """Identifie les insights clés sur les outputs."""
        insights = []
        
        raw = self.raw_differences
        analysis = self.analysis
        
        # Insights dimensionnels
        if raw["dimension_variations"]["same_dimensions"]:
            insights.append(f"Tous les outputs ont les mêmes dimensions ({raw['dimension_variations']['unique_dimensions'][0]})")
        
        # Insights de taille
        size_vars = raw["size_variations"]
        if size_vars["size_ratios"]["consistent_ratio"]:
            ratio = size_vars["size_ratios"]["common_ratio"]
            insights.append(f"Les outputs suivent un ratio de taille constant : ×{ratio:.2f}")
        
        # Insights chromatiques
        if analysis["color_patterns"]["uniform_palette_size"]:
            insights.append(f"Tous les outputs utilisent exactement {raw['color_variations']['color_counts_per_grid'][0]} couleurs")
        
        # Insights structurels
        if analysis["structural_patterns"]["structural_consistency"]["score"] > 0.8:
            insights.append("Les outputs ont une structure très similaire")
        
        # Insights d'évolution
        evolution = analysis["progression_patterns"]["output_evolution"]
        if evolution["becomes_more_complex"]:
            insights.append("Les outputs deviennent progressivement plus complexes")
        elif evolution["becomes_larger"]:
            insights.append("Les outputs augmentent progressivement en taille")
        
        return insights
    
    def _identify_anomalies(self) -> List[str]:
        """Identifie les anomalies dans les outputs."""
        anomalies = []
        
        raw = self.raw_differences
        
        # Anomalies de taille
        if raw["size_variations"]["total_cells_range"] > raw["size_variations"]["max_total_cells"] * 0.8:
            anomalies.append("Écart de taille extrême entre les outputs")
        
        # Anomalies chromatiques
        if raw["color_variations"]["color_count_range"] > 5:
            anomalies.append("Variation très importante du nombre de couleurs entre outputs")
        
        # Anomalies de densité
        if raw["density_variations"]["density_range"] > 0.8:
            anomalies.append("Variation extrême de densité entre outputs")
        
        # Anomalies structurelles
        complexity_scores = raw["structural_variations"]["complexity_scores"]
        if max(complexity_scores) > 3 * min(complexity_scores):
            anomalies.append("Écart de complexité très important entre outputs")
        
        return anomalies
    
    def _identify_strong_patterns(self) -> List[str]:
        """Identifie les patterns forts dans les outputs."""
        patterns = []
        
        analysis = self.analysis
        
        # Patterns dimensionnels
        if analysis["dimension_patterns"]["dimension_progression"]:
            prog = analysis["dimension_patterns"]["dimension_progression"]
            if prog["proportional_scaling"]:
                patterns.append("Mise à l'échelle proportionnelle des outputs")
        
        # Patterns chromatiques
        if analysis["color_patterns"]["palette_progression"]:
            prog = analysis["color_patterns"]["palette_progression"]
            if prog["arithmetic"]:
                patterns.append(f"Progression arithmétique du nombre de couleurs : {prog['step']:+d} par étape")
        
        # Patterns structurels
        if analysis["structural_patterns"]["complexity_trend"] == "increasing":
            patterns.append("Complexité croissante des outputs")
        
        # Cohérence forte
        if analysis["consistency_analysis"]["overall_consistency_score"] > 0.8:
            patterns.append("Très forte cohérence structurelle entre outputs")
        
        return patterns
    
    def _identify_output_characteristics(self) -> List[str]:
        """Identifie les caractéristiques spécifiques des outputs."""
        characteristics = []
        
        raw = self.raw_differences
        
        # Caractéristiques de remplissage
        fill_analysis = raw["structural_variations"]["fill_patterns"]
        total = len(self.output_grids)
        
        if len(fill_analysis["dense_grids"]) > total * 0.7:
            characteristics.append("Outputs majoritairement denses (>70% remplis)")
        elif len(fill_analysis["sparse_grids"]) > total * 0.7:
            characteristics.append("Outputs majoritairement épars (<30% remplis)")
        
        # Caractéristiques de taille
        sizes = raw["size_variations"]["total_cells_per_grid"]
        if all(s > 50 for s in sizes):
            characteristics.append("Tous les outputs sont de grande taille (>50 cellules)")
        elif all(s <= 20 for s in sizes):
            characteristics.append("Tous les outputs sont de petite taille (≤20 cellules)")
        
        # Caractéristiques chromatiques
        colors = raw["color_variations"]["color_counts_per_grid"]
        if all(c <= 3 for c in colors):
            characteristics.append("Palette limitée dans tous les outputs (≤3 couleurs)")
        elif any(c > 6 for c in colors):
            characteristics.append("Certains outputs ont une palette riche (>6 couleurs)")
        
        return characteristics
    
    def _generate_recommendations(self) -> List[str]:
        """Génère des recommandations d'analyse pour les outputs."""
        recommendations = []
        
        analysis = self.analysis
        
        # Recommandations basées sur la cohérence
        consistency = analysis["consistency_analysis"]["overall_consistency_score"]
        if consistency > 0.7:
            recommendations.append("Outputs cohérents - chercher des règles de transformation uniformes")
        else:
            recommendations.append("Outputs variables - analyser les transformations cas par cas")
        
        # Recommandations basées sur l'évolution
        evolution = analysis["progression_patterns"]["output_evolution"]["evolution_summary"]
        if evolution == "outputs_become_more_complex_overall":
            recommendations.append("Transformation progressive - analyser la séquence d'augmentation de complexité")
        elif evolution == "outputs_remain_stable":
            recommendations.append("Outputs stables - se concentrer sur les transformations input→output")
        
        # Recommandations basées sur la structure
        structural_score = analysis["structural_patterns"]["structural_consistency"]["score"]
        if structural_score > 0.8:
            recommendations.append("Structure similaire - chercher des patterns de placement ou de forme")
        else:
            recommendations.append("Structures diverses - analyser les règles de génération individuelles")
        
        return recommendations
    
    def _calculate_confidence_score(self) -> float:
        """Calcule un score de confiance pour l'analyse des outputs."""
        if len(self.output_grids) < 2:
            return 0.0
        
        scores = []
        
        # Score basé sur la cohérence
        consistency_score = self.analysis["consistency_analysis"]["overall_consistency_score"]
        scores.append(consistency_score)
        
        # Score basé sur le nombre d'exemples
        sample_score = min(1.0, len(self.output_grids) / 5.0)
        scores.append(sample_score)
        
        # Score basé sur la richesse des patterns
        pattern_score = 0.5
        if self.analysis["dimension_patterns"]["is_uniform_dimensions"]:
            pattern_score += 0.2
        if self.analysis["color_patterns"]["uniform_palette_size"]:
            pattern_score += 0.2
        if self.analysis["structural_patterns"]["structural_consistency"]["score"] > 0.6:
            pattern_score += 0.1
        pattern_score = min(1.0, pattern_score)
        scores.append(pattern_score)
        
        return sum(scores) / len(scores)
    
    def _convert_to_json_serializable(self, obj):
        """Convertit les types NumPy en types Python natifs pour JSON."""
        if isinstance(obj, np.integer):
            return int(obj)
        elif isinstance(obj, np.floating):
            return float(obj)
        elif isinstance(obj, (np.bool_, bool)):
            return bool(obj)
        elif isinstance(obj, np.ndarray):
            return obj.tolist()
        elif isinstance(obj, dict):
            # Convertir les clés ET les valeurs
            converted_dict = {}
            for k, v in obj.items():
                # Convertir la clé
                if isinstance(k, (np.integer, np.floating)):
                    converted_key = int(k) if isinstance(k, np.integer) else float(k)
                else:
                    converted_key = k
                # Convertir la valeur
                converted_dict[converted_key] = self._convert_to_json_serializable(v)
            return converted_dict
        elif isinstance(obj, (list, tuple)):
            return [self._convert_to_json_serializable(item) for item in obj]
        elif isinstance(obj, set):
            return list(obj)
        else:
            return obj
    
    def to_dict(self) -> Dict[str, Any]:
        """Retourne l'analyse complète sous forme de dictionnaire sérialisable JSON."""
        data = {
            "output_count": len(self.output_grids),
            "raw_differences": self.raw_differences,
            "analysis": self.analysis,
            "notable": self.notable
        }
        return self._convert_to_json_serializable(data)


def analyze_output_differences(output_grids: List[np.ndarray]) -> Dict[str, Any]:
    """Fonction utilitaire pour analyser les différences entre outputs."""
    analyzer = OutputDifferences(output_grids)
    return analyzer.to_dict()


# Exemple d'utilisation et test
if __name__ == "__main__":
    import argparse
    import json
    import sys
    import os
    
    # Forcer l'encodage UTF-8 pour éviter les problèmes d'affichage
    if sys.stdout.encoding != 'utf-8':
        sys.stdout.reconfigure(encoding='utf-8', errors='replace')
    
    def main():
        parser = argparse.ArgumentParser(description="Analyse des différences entre grilles output")
        parser.add_argument("--test", action="store_true", help="Exécuter un test avec des données d'exemple")
        parser.add_argument("--taskid", help="ID de la tâche ARC à analyser (ex: 007bbfb7). Si omis, analyse toutes les tâches")
        parser.add_argument("--subset", choices=["training", "evaluation"], default="training",
                           help="Subset de données (default: training)")
        args = parser.parse_args()
        
        if args.taskid:
            # Analyser une tâche ARC réelle
            from pathlib import Path
            
            task_file = Path(f"../../arcdata/{args.subset}/{args.taskid}.json")
            if not task_file.exists():
                print(f"❌ Fichier de tâche non trouvé: {task_file}")
                return
            
            with open(task_file, 'r') as f:
                task_data = json.load(f)
            
            train_examples = task_data.get('train', [])
            if len(train_examples) < 2:
                print(f"❌ Pas assez d'exemples train ({len(train_examples)}) pour analyser les différences")
                return
            
            # Extraire les grilles output
            output_grids = [np.array(ex['output']) for ex in train_examples]
            
            print(f"🔍 Analyse des différences entre outputs - Tâche {args.taskid}")
            print("=" * 60)
            
            analysis = analyze_output_differences(output_grids)
            
            # Afficher les résultats
            print(f"Nombre de grilles output analysées: {analysis['output_count']}")
            print(f"Subset: {args.subset}")
            
            print("\n📊 Insights clés:")
            for insight in analysis['notable']['key_insights']:
                print(f"  • {insight}")
            
            print("\n🔍 Patterns forts:")
            for pattern in analysis['notable']['strong_patterns']:
                print(f"  ✓ {pattern}")
            
            print("\n🏗️  Caractéristiques des outputs:")
            for char in analysis['notable']['output_characteristics']:
                print(f"  ▪ {char}")
            
            print("\n⚠️  Anomalies:")
            for anomaly in analysis['notable']['anomalies']:
                print(f"  ! {anomaly}")
            
            print("\n💡 Recommandations:")
            for rec in analysis['notable']['recommendations']:
                print(f"  → {rec}")
            
            print(f"\n📈 Score de confiance: {analysis['notable']['confidence_score']:.1%}")
            
            # Sauvegarder en JSON pour inspection
            import os
            results_dir = f"results/{args.subset}"
            os.makedirs(results_dir, exist_ok=True)
            output_file = f"{results_dir}/{args.taskid}_output_differences_test.json"
            with open(output_file, 'w') as f:
                json.dump(analysis, f, indent=2)
            print(f"\n💾 Résultats sauvegardés dans '{output_file}'")
        
        elif args.test:
            # Test avec des grilles d'exemple (outputs plus complexes)
            test_grids = [
                np.array([[0, 0, 0, 0, 7, 7, 0, 7, 7], [0, 0, 0, 7, 7, 7, 7, 7, 7], [0, 0, 0, 0, 7, 7, 0, 7, 7]]),
                np.array([[4, 0, 4, 0, 0, 0, 4, 0, 4], [0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 4, 0, 0, 0, 0, 0, 4, 0]]),
                np.array([[0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0, 0]])
            ]
            
            print("🔍 Test d'analyse des différences entre outputs")
            print("=" * 50)
            
            analysis = analyze_output_differences(test_grids)
            
            # Afficher les résultats
            print(f"Nombre de grilles analysées: {analysis['output_count']}")
            print("\n📊 Insights clés:")
            for insight in analysis['notable']['key_insights']:
                print(f"  • {insight}")
            
            print("\n🔍 Patterns forts:")
            for pattern in analysis['notable']['strong_patterns']:
                print(f"  ✓ {pattern}")
            
            print(f"\n📈 Score de confiance: {analysis['notable']['confidence_score']:.1%}")
            
            # Sauvegarder en JSON pour inspection
            import os
            os.makedirs("results", exist_ok=True)
            with open('results/test_output_differences.json', 'w') as f:
                json.dump(analysis, f, indent=2)
            print("\n💾 Résultats sauvegardés dans 'results/test_output_differences.json'")
        
        else:
            # Analyser toutes les tâches du subset
            from pathlib import Path
            import glob
            
            subset_dir = Path(f"../../arcdata/{args.subset}")
            if not subset_dir.exists():
                print(f"❌ Répertoire non trouvé: {subset_dir}")
                return
            
            task_files = [f for f in subset_dir.glob("*.json") if not f.name.endswith("_arc_analysis.json")]
            if not task_files:
                print(f"[-] Aucune tâche trouvée dans {subset_dir}")
                return
            
            print(f"[*] Analyse de toutes les taches du subset {args.subset}")
            print(f"[*] {len(task_files)} taches trouvees")
            print("=" * 60)
            
            successful_analyses = 0
            failed_analyses = 0
            
            for task_file in sorted(task_files):
                task_id = task_file.stem
                try:
                    print(f"\n[>] Analyse de la tache {task_id}...")
                except (BrokenPipeError, OSError):
                    # Le pipe a été fermé (ex: Select-Object -First N)
                    break
                
                try:
                    with open(task_file, 'r') as f:
                        task_data = json.load(f)
                    
                    train_examples = task_data.get('train', [])
                    if len(train_examples) < 2:
                        print(f"  [!] Pas assez d'exemples train ({len(train_examples)})")
                        failed_analyses += 1
                        continue
                    
                    # Extraire les grilles output
                    output_grids = [np.array(example['output']) for example in train_examples]
                    
                    # Analyser
                    analysis = analyze_output_differences(output_grids)
                    
                    # Sauvegarder
                    import os
                    results_dir = f"results/{args.subset}"
                    os.makedirs(results_dir, exist_ok=True)
                    output_file = f"{results_dir}/{task_id}_output_differences.json"
                    with open(output_file, 'w') as f:
                        json.dump(analysis, f, indent=2)
                    
                    # Afficher résumé
                    confidence = analysis['notable']['confidence_score']
                    insights_count = len(analysis['notable']['key_insights'])
                    patterns_count = len(analysis['notable']['strong_patterns'])
                    
                    try:
                        print(f"  [+] Termine - Confiance: {confidence:.1%}, {insights_count} insights, {patterns_count} patterns")
                    except (BrokenPipeError, OSError):
                        break
                    successful_analyses += 1
                    
                except Exception as e:
                    print(f"  [-] Erreur: {str(e)[:50]}...")
                    failed_analyses += 1
            
            print(f"\n[*] Resume final:")
            print(f"  [+] Analyses reussies: {successful_analyses}")
            print(f"  [-] Analyses echouees: {failed_analyses}")
            print(f"  [*] Resultats dans: results/{args.subset}/")
            
            if successful_analyses == 0:
                print("\n[!] Aucune analyse reussie. Utilisez --test pour un exemple ou --taskid pour une tache specifique.")
            print("  python output_differences.py --test")
    
    main()