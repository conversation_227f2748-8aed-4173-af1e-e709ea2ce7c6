#!/usr/bin/env python3
"""
Générateur de Scénarios AGI pour les Tâches d'Évaluation

Ce script utilise les corrélations apprises sur les données d'entraînement
pour prédire et générer des scénarios AGI pour les tâches d'évaluation.

Basé sur le processus décrit dans PROCESS_GUIDE.md
"""

import json
import os
import sys
from pathlib import Path
from typing import Dict, List, Any, Tuple
from collections import Counter
import numpy as np

class EvaluationScenarioGenerator:
    """
    Générateur de scénarios AGI pour les tâches d'évaluation
    basé sur les corrélations apprises des données d'entraînement
    """
    
    def __init__(self):
        self.base_dir = Path(__file__).parent
        self.results_dir = self.base_dir / "results" / "evaluation"
        self.scenarios_dir = self.base_dir / "scenarios" / "evaluation"
        self.correlation_file = self.base_dir / "enhanced_correlation_analysis" / "enhanced_correlation_analysis.json"
        
        # Créer le répertoire de sortie s'il n'existe pas
        self.scenarios_dir.mkdir(parents=True, exist_ok=True)
        
        # Charger les données de corrélation
        self.correlations = self._load_correlations()
        self.command_priorities = self._extract_command_priorities()
        
    def _load_correlations(self) -> Dict[str, Any]:
        """Charge les données de corrélation depuis le fichier d'analyse."""
        try:
            with open(self.correlation_file, 'r', encoding='utf-8') as f:
                return json.load(f)
        except FileNotFoundError:
            print(f"❌ Fichier de corrélation non trouvé: {self.correlation_file}")
            return {}
        except json.JSONDecodeError as e:
            print(f"❌ Erreur de parsing JSON: {e}")
            return {}
    
    def _extract_command_priorities(self) -> Dict[str, float]:
        """Extrait les priorités des commandes basées sur leur fréquence."""
        if not self.correlations:
            return {}
            
        command_freq = self.correlations.get('metadata', {}).get('enhanced_command_frequency', {})
        total_tasks = self.correlations.get('metadata', {}).get('total_tasks_analyzed', 1)
        
        # Calculer les priorités normalisées
        priorities = {}
        for command, count in command_freq.items():
            priorities[command] = count / total_tasks
            
        return priorities
    
    def _load_task_characteristics(self, task_id: str) -> Dict[str, Any]:
        """Charge les caractéristiques d'une tâche d'évaluation."""
        characteristics = {}
        
        # Fichiers de caractéristiques à charger
        files_to_load = [
            f"{task_id}_io_differences.json",
            f"{task_id}_input_differences.json", 
            f"{task_id}_output_differences.json",
            f"{task_id}_test0_input_params.json"
        ]
        
        # Charger aussi les fichiers train disponibles
        for i in range(10):  # Jusqu'à 10 exemples d'entraînement
            train_file = f"{task_id}_train{i}_input_params.json"
            if (self.results_dir / train_file).exists():
                files_to_load.append(train_file)
        
        for filename in files_to_load:
            file_path = self.results_dir / filename
            if file_path.exists():
                try:
                    with open(file_path, 'r', encoding='utf-8') as f:
                        data = json.load(f)
                        characteristics[filename.replace('.json', '')] = data
                except (json.JSONDecodeError, FileNotFoundError) as e:
                    print(f"⚠️  Erreur lors du chargement de {filename}: {e}")
                    
        return characteristics
    
    def _extract_key_features(self, characteristics: Dict[str, Any]) -> Dict[str, Any]:
        """Extrait les caractéristiques clés pour la prédiction."""
        features = {
            'input_shape': None,
            'output_shape': None,
            'size_change': 'same',
            'color_count': 0,
            'unique_colors': [],
            'grid_complexity': 0,
            'has_patterns': False,
            'symmetry_detected': False
        }
        
        # Extraire les informations de forme depuis test0_input_params
        test_params = characteristics.get(f'{list(characteristics.keys())[0].split("_")[0]}_test0_input_params', {})
        if test_params:
            features['input_shape'] = test_params.get('shape', None)
            features['color_count'] = test_params.get('unique_colors_count', 0)
            features['unique_colors'] = test_params.get('unique_colors', [])
            features['grid_complexity'] = test_params.get('non_zero_count', 0)
        
        # Analyser les différences IO pour déterminer le changement de taille
        io_diff = characteristics.get(f'{list(characteristics.keys())[0].split("_")[0]}_io_differences', {})
        if io_diff:
            size_changes = io_diff.get('size_changes', [])
            if size_changes:
                if all(change == 'same' for change in size_changes):
                    features['size_change'] = 'same'
                elif any('larger' in str(change) for change in size_changes):
                    features['size_change'] = 'larger'
                elif any('smaller' in str(change) for change in size_changes):
                    features['size_change'] = 'smaller'
        
        return features
    
    def _predict_commands(self, features: Dict[str, Any]) -> List[str]:
        """Prédit les commandes les plus probables basées sur les caractéristiques."""
        predicted_commands = []
        
        # Toujours commencer par TRANSFERT (86.5% des cas selon l'analyse)
        if self.command_priorities.get('TRANSFERT', 0) > 0.8:
            predicted_commands.append('TRANSFERT')
        
        # Prédiction basée sur la forme d'entrée
        input_shape = features.get('input_shape')
        if input_shape:
            shape_key = f"input_shape={tuple(input_shape)}"
            shape_correlations = self.correlations.get('strong_correlations', {}).get(shape_key, {})
            
            if shape_correlations:
                commands = shape_correlations.get('commands', {})
                for command, data in commands.items():
                    if data.get('percentage', 0) > 20:  # Seuil de 20%
                        if command not in predicted_commands:
                            predicted_commands.append(command)
        
        # Prédiction basée sur le changement de taille
        size_change = features.get('size_change', 'same')
        if size_change == 'same':
            # Privilégier MOVE et EDIT pour les tâches sans changement de taille
            if 'MOVE' not in predicted_commands and self.command_priorities.get('MOVE', 0) > 0.4:
                predicted_commands.append('MOVE')
            if 'EDIT' not in predicted_commands and self.command_priorities.get('EDIT', 0) > 0.2:
                predicted_commands.append('EDIT')
        elif size_change == 'larger':
            # Privilégier RESIZE et MULTIPLY pour l'agrandissement
            if 'RESIZE' not in predicted_commands:
                predicted_commands.append('RESIZE')
        elif size_change == 'smaller':
            # Privilégier EXTRACT pour la réduction
            if 'EXTRACT' not in predicted_commands:
                predicted_commands.append('EXTRACT')
        
        # Prédiction basée sur la complexité des couleurs
        color_count = features.get('color_count', 0)
        if color_count > 5:
            # Beaucoup de couleurs -> probablement FILL ou FLOODFILL
            if 'FILL' not in predicted_commands and self.command_priorities.get('FILL', 0) > 0.15:
                predicted_commands.append('FILL')
        elif color_count <= 3:
            # Peu de couleurs -> probablement des transformations géométriques
            if 'FLIP' not in predicted_commands and self.command_priorities.get('FLIP', 0) > 0.05:
                predicted_commands.append('FLIP')
        
        # Limiter à 5 commandes maximum pour éviter la sur-prédiction
        return predicted_commands[:5]
    
    def _generate_scenario_content(self, task_id: str, predicted_commands: List[str], features: Dict[str, Any]) -> str:
        """Génère le contenu du scénario AGI."""
        lines = []
        
        # En-tête avec commentaire
        lines.append(f"# Scénario AGI prédit pour la tâche {task_id}")
        lines.append(f"# Généré automatiquement basé sur les corrélations d'entraînement")
        lines.append("")
        
        # Commandes prédites
        for command in predicted_commands:
            if command == 'TRANSFERT':
                # TRANSFERT avec initialisation basée sur la forme d'entrée
                input_shape = features.get('input_shape', [3, 3])
                lines.append(f"TRANSFERT")
            elif command == 'MOVE':
                lines.append("MOVE")
            elif command == 'EDIT':
                lines.append("EDIT")
            elif command == 'RESIZE':
                lines.append("RESIZE")
            elif command == 'FILL':
                lines.append("FILL")
            elif command == 'FLIP':
                lines.append("FLIP")
            elif command == 'EXTRACT':
                lines.append("EXTRACT")
            else:
                lines.append(command)
        
        lines.append("END")
        
        return "\n".join(lines)
    
    def generate_scenario_for_task(self, task_id: str) -> bool:
        """Génère un scénario AGI pour une tâche spécifique."""
        print(f"🔍 Génération du scénario pour la tâche {task_id}")
        
        # Charger les caractéristiques de la tâche
        characteristics = self._load_task_characteristics(task_id)
        if not characteristics:
            print(f"❌ Aucune caractéristique trouvée pour {task_id}")
            return False
        
        # Extraire les caractéristiques clés
        features = self._extract_key_features(characteristics)
        
        # Prédire les commandes
        predicted_commands = self._predict_commands(features)
        
        if not predicted_commands:
            print(f"❌ Aucune commande prédite pour {task_id}")
            return False
        
        # Générer le contenu du scénario
        scenario_content = self._generate_scenario_content(task_id, predicted_commands, features)
        
        # Sauvegarder le scénario
        scenario_file = self.scenarios_dir / f"{task_id}_TEST0_PREDICTED.agi"
        try:
            with open(scenario_file, 'w', encoding='utf-8') as f:
                f.write(scenario_content)
            
            print(f"✅ Scénario généré: {scenario_file}")
            print(f"   Commandes prédites: {', '.join(predicted_commands)}")
            return True
            
        except Exception as e:
            print(f"❌ Erreur lors de la sauvegarde: {e}")
            return False

    def generate_all_evaluation_scenarios(self) -> Dict[str, Any]:
        """Génère des scénarios pour toutes les tâches d'évaluation."""
        print("🚀 Génération des scénarios AGI pour toutes les tâches d'évaluation")
        print("=" * 70)

        # Trouver toutes les tâches d'évaluation
        evaluation_tasks = set()
        for file_path in self.results_dir.glob("*_test0_input_params.json"):
            task_id = file_path.stem.replace("_test0_input_params", "")
            evaluation_tasks.add(task_id)

        evaluation_tasks = sorted(list(evaluation_tasks))
        print(f"📊 {len(evaluation_tasks)} tâches d'évaluation trouvées")

        # Statistiques de génération
        stats = {
            'total_tasks': len(evaluation_tasks),
            'successful_generations': 0,
            'failed_generations': 0,
            'command_distribution': Counter(),
            'generated_files': []
        }

        # Générer les scénarios
        for i, task_id in enumerate(evaluation_tasks, 1):
            print(f"\n[{i:3d}/{len(evaluation_tasks)}] {task_id}")

            success = self.generate_scenario_for_task(task_id)
            if success:
                stats['successful_generations'] += 1
                stats['generated_files'].append(f"{task_id}_TEST0_PREDICTED.agi")

                # Compter les commandes prédites
                characteristics = self._load_task_characteristics(task_id)
                features = self._extract_key_features(characteristics)
                predicted_commands = self._predict_commands(features)
                for cmd in predicted_commands:
                    stats['command_distribution'][cmd] += 1
            else:
                stats['failed_generations'] += 1

        # Afficher les statistiques finales
        self._print_generation_stats(stats)

        # Sauvegarder les statistiques
        stats_file = self.scenarios_dir / "generation_stats.json"
        with open(stats_file, 'w', encoding='utf-8') as f:
            # Convertir Counter en dict pour la sérialisation JSON
            stats_copy = stats.copy()
            stats_copy['command_distribution'] = dict(stats['command_distribution'])
            json.dump(stats_copy, f, indent=2, ensure_ascii=False)

        return stats

    def _print_generation_stats(self, stats: Dict[str, Any]) -> None:
        """Affiche les statistiques de génération."""
        print("\n" + "=" * 70)
        print("📈 STATISTIQUES DE GÉNÉRATION")
        print("=" * 70)

        print(f"✅ Scénarios générés avec succès: {stats['successful_generations']}")
        print(f"❌ Échecs de génération: {stats['failed_generations']}")
        print(f"📊 Taux de succès: {stats['successful_generations']/stats['total_tasks']*100:.1f}%")

        print(f"\n🎯 DISTRIBUTION DES COMMANDES PRÉDITES:")
        print("-" * 40)

        command_dist = stats['command_distribution']
        total_predictions = sum(command_dist.values())

        for command, count in command_dist.most_common():
            percentage = count / total_predictions * 100
            print(f"  {command:15s}: {count:3d} ({percentage:5.1f}%)")

        print(f"\n📁 Fichiers générés dans: {self.scenarios_dir}")
        print(f"   Total: {len(stats['generated_files'])} fichiers .agi")

    def validate_predictions(self, sample_size: int = 10) -> None:
        """Valide les prédictions sur un échantillon de tâches."""
        print(f"\n🔍 VALIDATION DES PRÉDICTIONS (échantillon de {sample_size})")
        print("-" * 50)

        # Sélectionner un échantillon aléatoire
        evaluation_tasks = list(self.results_dir.glob("*_test0_input_params.json"))
        sample_tasks = np.random.choice(evaluation_tasks, min(sample_size, len(evaluation_tasks)), replace=False)

        for task_file in sample_tasks:
            task_id = task_file.stem.replace("_test0_input_params", "")

            # Charger les caractéristiques
            characteristics = self._load_task_characteristics(task_id)
            features = self._extract_key_features(characteristics)
            predicted_commands = self._predict_commands(features)

            print(f"\n📋 Tâche: {task_id}")
            print(f"   Forme d'entrée: {features.get('input_shape', 'N/A')}")
            print(f"   Changement de taille: {features.get('size_change', 'N/A')}")
            print(f"   Nombre de couleurs: {features.get('color_count', 'N/A')}")
            print(f"   Commandes prédites: {', '.join(predicted_commands)}")

    def compare_with_training_patterns(self) -> Dict[str, Any]:
        """Compare les patterns d'évaluation avec ceux d'entraînement."""
        print("\n🔄 COMPARAISON AVEC LES PATTERNS D'ENTRAÎNEMENT")
        print("-" * 50)

        # Analyser les patterns des tâches d'évaluation
        eval_patterns = {
            'shape_distribution': Counter(),
            'size_change_distribution': Counter(),
            'color_distribution': Counter(),
            'command_predictions': Counter()
        }

        evaluation_tasks = []
        for file_path in self.results_dir.glob("*_test0_input_params.json"):
            task_id = file_path.stem.replace("_test0_input_params", "")
            evaluation_tasks.append(task_id)

        for task_id in evaluation_tasks:
            characteristics = self._load_task_characteristics(task_id)
            features = self._extract_key_features(characteristics)
            predicted_commands = self._predict_commands(features)

            # Collecter les patterns
            if features.get('input_shape'):
                eval_patterns['shape_distribution'][tuple(features['input_shape'])] += 1

            eval_patterns['size_change_distribution'][features.get('size_change', 'unknown')] += 1
            eval_patterns['color_distribution'][features.get('color_count', 0)] += 1

            for cmd in predicted_commands:
                eval_patterns['command_predictions'][cmd] += 1

        # Afficher la comparaison
        print(f"📊 Analyse de {len(evaluation_tasks)} tâches d'évaluation:")

        print(f"\n🔷 Formes les plus communes:")
        for shape, count in eval_patterns['shape_distribution'].most_common(5):
            print(f"   {shape}: {count} tâches")

        print(f"\n📏 Distribution des changements de taille:")
        for change, count in eval_patterns['size_change_distribution'].items():
            print(f"   {change}: {count} tâches")

        print(f"\n🎨 Distribution des couleurs:")
        for color_count, count in eval_patterns['color_distribution'].most_common(5):
            print(f"   {color_count} couleurs: {count} tâches")

        return eval_patterns


def main():
    """Point d'entrée principal."""
    print("🎯 GÉNÉRATEUR DE SCÉNARIOS AGI POUR L'ÉVALUATION")
    print("=" * 60)
    print("Basé sur les corrélations apprises des données d'entraînement")
    print("Processus décrit dans PROCESS_GUIDE.md")
    print()

    # Initialiser le générateur
    generator = EvaluationScenarioGenerator()

    if not generator.correlations:
        print("❌ Impossible de charger les données de corrélation.")
        print("   Assurez-vous d'avoir exécuté l'analyse de corrélation d'abord.")
        return

    print(f"✅ Données de corrélation chargées:")
    print(f"   - {generator.correlations.get('metadata', {}).get('total_tasks_analyzed', 0)} tâches analysées")
    print(f"   - {len(generator.command_priorities)} types de commandes")

    # Générer tous les scénarios
    stats = generator.generate_all_evaluation_scenarios()

    # Validation sur un échantillon
    generator.validate_predictions(sample_size=5)

    # Comparaison avec les patterns d'entraînement
    eval_patterns = generator.compare_with_training_patterns()

    print(f"\n🎉 GÉNÉRATION TERMINÉE")
    print(f"   Consultez les fichiers dans: {generator.scenarios_dir}")


if __name__ == "__main__":
    main()
