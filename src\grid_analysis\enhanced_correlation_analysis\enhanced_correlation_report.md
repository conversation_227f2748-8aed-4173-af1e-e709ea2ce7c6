# Analyse de Corrélations Améliorée ARC

## Métadonnées

- **Tâches analysées**: 401
- **Corrélations identifiées**: 332

## Fréquence des Commandes Améliorées

- **TRANSFERT**: 347 utilisations (86.5%)
- **RESIZE**: 26 utilisations (6.5%)
- **MULTIPLY MOTIF**: 3 utilisations (0.7%)
- **FLOODFILL**: 31 utilisations (7.7%)
- **MOVE**: 167 utilisations (41.6%)
- **REPLACE**: 41 utilisations (10.2%)
- **MOVE MOTIF**: 54 utilisations (13.5%)
- **EDITS**: 66 utilisations (16.5%)
- **EXTRACT**: 63 utilisations (15.7%)
- **FILL**: 78 utilisations (19.5%)

## Corrélations Fortes avec Classification Améliorée

### test0_input_params_height>0

Occurrences totales: 1174

- **TRANSFERT**: 347 fois (29.56%) sur 347 utilisations totales

### test0_input_params_width>0

Occurrences totales: 1174

- **TRANSFERT**: 347 fois (29.56%) sur 347 utilisations totales

### test0_input_params_total_cells>0

Occurrences totales: 1174

- **TRANSFERT**: 347 fois (29.56%) sur 347 utilisations totales

### test0_input_params_color_count>0

Occurrences totales: 1174

- **TRANSFERT**: 347 fois (29.56%) sur 347 utilisations totales

### train0_input_params_height>0

Occurrences totales: 1174

- **TRANSFERT**: 347 fois (29.56%) sur 347 utilisations totales

### train0_input_params_width>0

Occurrences totales: 1174

- **TRANSFERT**: 347 fois (29.56%) sur 347 utilisations totales

### train0_input_params_total_cells>0

Occurrences totales: 1174

- **TRANSFERT**: 347 fois (29.56%) sur 347 utilisations totales

### train0_input_params_color_count>0

Occurrences totales: 1174

- **TRANSFERT**: 347 fois (29.56%) sur 347 utilisations totales

### dimension_change=(0, 0)

Occurrences totales: 713

- **TRANSFERT**: 257 fois (36.04%) sur 347 utilisations totales

### size_change=0

Occurrences totales: 713

- **TRANSFERT**: 257 fois (36.04%) sur 347 utilisations totales

### input_dimension_consistency>0

Occurrences totales: 627

- **TRANSFERT**: 179 fois (28.55%) sur 347 utilisations totales

### output_dimension_consistency>0

Occurrences totales: 618

- **TRANSFERT**: 179 fois (28.96%) sur 347 utilisations totales

### output_dimension_consistency=0

Occurrences totales: 556

- **TRANSFERT**: 168 fois (30.22%) sur 347 utilisations totales

### input_dimension_consistency=0

Occurrences totales: 547

- **TRANSFERT**: 168 fois (30.71%) sur 347 utilisations totales

### output_shape=(10, 10)

Occurrences totales: 199

- **TRANSFERT**: 64 fois (32.16%) sur 347 utilisations totales

## Analyse par Catégorie de Commande

### Movement Operations

- **test0_input_params_height>0**: 221 occurrences
  - MOVE: 167
  - MOVE MOTIF: 54
- **test0_input_params_width>0**: 221 occurrences
  - MOVE: 167
  - MOVE MOTIF: 54
- **test0_input_params_total_cells>0**: 221 occurrences
  - MOVE: 167
  - MOVE MOTIF: 54
- **test0_input_params_color_count>0**: 221 occurrences
  - MOVE: 167
  - MOVE MOTIF: 54
- **train0_input_params_height>0**: 221 occurrences
  - MOVE: 167
  - MOVE MOTIF: 54

### Transformation Operations

- **test0_input_params_height>0**: 82 occurrences
  - MULTIPLY MOTIF: 3
  - FLIP ROTATE MOTIF: 1
  - ROTATE MOTIF: 2
  - FLIP MOTIF: 12
  - FLIP: 39
  - ROTATE: 17
  - MULTIPLY: 3
  - DIVIDE MOTIF: 1
  - DIVIDE: 3
  - FLIP ROTATE: 1
- **test0_input_params_width>0**: 82 occurrences
  - MULTIPLY MOTIF: 3
  - FLIP ROTATE MOTIF: 1
  - ROTATE MOTIF: 2
  - FLIP MOTIF: 12
  - FLIP: 39
  - ROTATE: 17
  - MULTIPLY: 3
  - DIVIDE MOTIF: 1
  - DIVIDE: 3
  - FLIP ROTATE: 1
- **test0_input_params_total_cells>0**: 82 occurrences
  - MULTIPLY MOTIF: 3
  - FLIP ROTATE MOTIF: 1
  - ROTATE MOTIF: 2
  - FLIP MOTIF: 12
  - FLIP: 39
  - ROTATE: 17
  - MULTIPLY: 3
  - DIVIDE MOTIF: 1
  - DIVIDE: 3
  - FLIP ROTATE: 1
- **test0_input_params_color_count>0**: 82 occurrences
  - MULTIPLY MOTIF: 3
  - FLIP ROTATE MOTIF: 1
  - ROTATE MOTIF: 2
  - FLIP MOTIF: 12
  - FLIP: 39
  - ROTATE: 17
  - MULTIPLY: 3
  - DIVIDE MOTIF: 1
  - DIVIDE: 3
  - FLIP ROTATE: 1
- **train0_input_params_height>0**: 82 occurrences
  - MULTIPLY MOTIF: 3
  - FLIP ROTATE MOTIF: 1
  - ROTATE MOTIF: 2
  - FLIP MOTIF: 12
  - FLIP: 39
  - ROTATE: 17
  - MULTIPLY: 3
  - DIVIDE MOTIF: 1
  - DIVIDE: 3
  - FLIP ROTATE: 1

### Basic Operations

- **test0_input_params_height>0**: 639 occurrences
  - TRANSFERT: 347
  - RESIZE: 26
  - REPLACE: 41
  - EXTRACT: 63
  - FILL: 78
  - EDIT: 84
- **test0_input_params_width>0**: 639 occurrences
  - TRANSFERT: 347
  - RESIZE: 26
  - REPLACE: 41
  - EXTRACT: 63
  - FILL: 78
  - EDIT: 84
- **test0_input_params_total_cells>0**: 639 occurrences
  - TRANSFERT: 347
  - RESIZE: 26
  - REPLACE: 41
  - EXTRACT: 63
  - FILL: 78
  - EDIT: 84
- **test0_input_params_color_count>0**: 639 occurrences
  - TRANSFERT: 347
  - RESIZE: 26
  - REPLACE: 41
  - EXTRACT: 63
  - FILL: 78
  - EDIT: 84
- **train0_input_params_height>0**: 639 occurrences
  - TRANSFERT: 347
  - RESIZE: 26
  - REPLACE: 41
  - EXTRACT: 63
  - FILL: 78
  - EDIT: 84

## Patterns Dimensionnels Améliorés

### Dimension Change

#### dimension_change=(0, 0)

- **Opérations de mouvement**: 125
- **Opérations de transformation**: 42
- **Total**: 713
- **Top commandes**: TRANSFERT, MOVE, FILL

#### dimension_change=(3, 3)

- **Opérations de mouvement**: 3
- **Opérations de transformation**: 8
- **Total**: 29
- **Top commandes**: TRANSFERT, RESIZE, FLIP

#### dimension_change=(5, 9)

- **Opérations de mouvement**: 19
- **Opérations de transformation**: 0
- **Total**: 21
- **Top commandes**: MOVE, INIT, EDITS

#### dimension_change=(6, 6)

- **Opérations de mouvement**: 7
- **Opérations de transformation**: 2
- **Total**: 20
- **Top commandes**: MOVE, TRANSFERT, RESIZE

#### dimension_change=(-4, -4)

- **Opérations de mouvement**: 3
- **Opérations de transformation**: 0
- **Total**: 17
- **Top commandes**: TRANSFERT, EXTRACT, MOVE MOTIF

### Input Shape

#### input_shape=(10, 10)

- **Opérations de mouvement**: 24
- **Opérations de transformation**: 3
- **Total**: 181
- **Top commandes**: TRANSFERT, MOVE, EDITS

#### input_shape=(3, 3)

- **Opérations de mouvement**: 13
- **Opérations de transformation**: 19
- **Total**: 129
- **Top commandes**: TRANSFERT, RESIZE, INIT

#### input_shape=(9, 9)

- **Opérations de mouvement**: 11
- **Opérations de transformation**: 0
- **Total**: 51
- **Top commandes**: TRANSFERT, MOVE, EDIT

#### input_shape=(13, 13)

- **Opérations de mouvement**: 7
- **Opérations de transformation**: 3
- **Total**: 36
- **Top commandes**: TRANSFERT, MOVE MOTIF, EXTRACT

#### input_shape=(11, 11)

- **Opérations de mouvement**: 8
- **Opérations de transformation**: 3
- **Total**: 34
- **Top commandes**: TRANSFERT, MOVE MOTIF, MOVE

### Output Shape

#### output_shape=(10, 10)

- **Opérations de mouvement**: 42
- **Opérations de transformation**: 5
- **Total**: 199
- **Top commandes**: TRANSFERT, MOVE, FILL

#### output_shape=(3, 3)

- **Opérations de mouvement**: 10
- **Opérations de transformation**: 8
- **Total**: 128
- **Top commandes**: TRANSFERT, EXTRACT, INIT

#### output_shape=(9, 9)

- **Opérations de mouvement**: 32
- **Opérations de transformation**: 4
- **Total**: 77
- **Top commandes**: MOVE, TRANSFERT, RESIZE

#### output_shape=(4, 4)

- **Opérations de mouvement**: 20
- **Opérations de transformation**: 6
- **Total**: 70
- **Top commandes**: TRANSFERT, MOVE MOTIF, EXTRACT

#### output_shape=(6, 6)

- **Opérations de mouvement**: 4
- **Opérations de transformation**: 8
- **Total**: 56
- **Top commandes**: TRANSFERT, FILL, EDITS

### Size Change

#### size_change=0

- **Opérations de mouvement**: 125
- **Opérations de transformation**: 42
- **Total**: 713
- **Top commandes**: TRANSFERT, MOVE, FILL

#### size_change<0

- **Opérations de mouvement**: 51
- **Opérations de transformation**: 18
- **Total**: 305
- **Top commandes**: TRANSFERT, EXTRACT, INIT

#### size_change>0

- **Opérations de mouvement**: 45
- **Opérations de transformation**: 22
- **Total**: 156
- **Top commandes**: MOVE, TRANSFERT, RESIZE

## Insights de l'Analyse Améliorée

### Mouvement vs Transformation

- **Simple Movement**: 167 (41.6%) - Opérations de mouvement simple sans COLOR
- **Movement With Color**: 54 (13.5%) - Opérations de mouvement avec COLOR
- **Transformations**: 82 (20.4%) - Opérations de transformation géométrique

### Indicateurs de Complexité

- **Transformations géométriques totales**: 0 (0.0%)
  - Flip horizontal: 0
  - Flip vertical: 0
  - Rotate right: 0

### Patterns Stratégiques

- **Movement Dominant**: ✓
- **Editing Prevalent**: ✗
- **Filling Common**: ✗
- **Transformation Rare**: ✗

## Recommandations Basées sur l'Analyse Améliorée

### Pour le Développement de Modèles

- Prioriser l'apprentissage des opérations MOVE (46.5% des cas)
- Développer des modules spécialisés pour MOVE vs MOVE MOTIF
- Intégrer la détection des transformations géométriques simples
- Optimiser pour les opérations d'édition fréquentes (41.8%)

### Pour l'Architecture du Système

- Implémenter un pipeline mouvement → transformation → édition
- Créer des chemins d'exécution optimisés pour chaque catégorie
- Développer des heuristiques basées sur les caractéristiques dimensionnelles
