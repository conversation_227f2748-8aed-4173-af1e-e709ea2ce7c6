#!/usr/bin/env python3
"""
Analyseur de correspondances entre caractéristiques des puzzles ARC et scénarios de résolution.

Ce script analyse les fichiers JSON de caractéristiques et les scénarios AGI correspondants
pour identifier les patterns et correspondances entre les propriétés des puzzles et les commandes utilisées.
"""

import json
import os
import re
from pathlib import Path
from collections import defaultdict, Counter
from typing import Dict, List, Any, Tuple, Optional
import logging

# Configuration du logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class FeatureScenarioAnalyzer:
    """Analyseur principal pour les correspondances caractéristiques-scénarios."""
    
    def __init__(self, results_dir: str, scenarios_dir: str):
        self.results_dir = Path(results_dir)
        self.scenarios_dir = Path(scenarios_dir)
        self.task_data = {}
        self.correlations = defaultdict(lambda: defaultdict(int))
        self.command_stats = Counter()
        
    def load_task_characteristics(self, task_id: str) -> Dict[str, Any]:
        """Charge toutes les caractéristiques d'une tâche donnée."""
        characteristics = {}
        
        # Types de fichiers à charger selon la spécification
        file_types = [
            'io_differences',
            'input_differences', 
            'output_differences',
            'test0_input_params',
            'train0_input_params',
            'train1_input_params',
            'train2_input_params'
        ]
        
        for file_type in file_types:
            file_path = self.results_dir / f"{task_id}_{file_type}.json"
            if file_path.exists():
                try:
                    with open(file_path, 'r', encoding='utf-8') as f:
                        characteristics[file_type] = json.load(f)
                except Exception as e:
                    logger.warning(f"Erreur lors du chargement de {file_path}: {e}")
            else:
                logger.debug(f"Fichier non trouvé: {file_path}")
                
        return characteristics
    
    def load_scenario(self, task_id: str) -> Optional[str]:
        """Charge le scénario AGI pour une tâche donnée."""
        scenario_path = self.scenarios_dir / f"{task_id}_TEST0_REDUCED.agi"
        
        if scenario_path.exists():
            try:
                with open(scenario_path, 'r', encoding='utf-8') as f:
                    return f.read().strip()
            except Exception as e:
                logger.warning(f"Erreur lors du chargement du scénario {scenario_path}: {e}")
        else:
            logger.debug(f"Scénario non trouvé: {scenario_path}")
            
        return None
    
    def extract_commands_from_scenario(self, scenario: str) -> List[str]:
        """Extrait les commandes AGI d'un scénario."""
        if not scenario:
            return []
            
        commands = []
        lines = scenario.split('\n')
        
        for line in lines:
            line = line.strip()
            if not line:
                continue
                
            # Extraction des commandes principales
            if line.startswith('RESIZE'):
                commands.append('RESIZE')
            elif line.startswith('MOTIF'):
                commands.append('MOTIF')
                # Extraction des sous-commandes dans MOTIF
                if 'CUT' in line:
                    commands.append('CUT')
                if 'MULTIPLY' in line:
                    commands.append('MULTIPLY')
                if 'PASTE' in line:
                    commands.append('PASTE')
                if 'COLOR' in line:
                    commands.append('COLOR')
            elif line.startswith('TRANSFERT'):
                commands.append('TRANSFERT')
            elif line.startswith('FILL'):
                commands.append('FILL')
            elif line.startswith('EDIT'):
                commands.append('EDIT')
            elif line.startswith('COPY'):
                commands.append('COPY')
            elif line.startswith('MOVE'):
                commands.append('MOVE')
            elif line.startswith('ROTATE'):
                commands.append('ROTATE')
            elif line.startswith('FLIP'):
                commands.append('FLIP')
                
        return commands
    
    def extract_key_features(self, characteristics: Dict[str, Any]) -> Dict[str, Any]:
        """Extrait les caractéristiques clés des données JSON."""
        features = {}
        
        # Caractéristiques des différences IO
        if 'io_differences' in characteristics:
            io_data = characteristics['io_differences']
            if 'raw_differences' in io_data:
                raw_diff = io_data['raw_differences']
                if 'transformation_data' in raw_diff and raw_diff['transformation_data']:
                    first_transform = raw_diff['transformation_data'][0]
                    features['input_shape'] = tuple(first_transform.get('input_shape', []))
                    features['output_shape'] = tuple(first_transform.get('output_shape', []))
                    features['dimension_change'] = tuple(first_transform.get('dimension_change', []))
                    features['size_change'] = first_transform.get('size_change', 0)
        
        # Caractéristiques des paramètres d'entrée
        for param_type in ['test0_input_params', 'train0_input_params']:
            if param_type in characteristics:
                params = characteristics[param_type]
                if 'parameters' in params:
                    p = params['parameters']
                    features[f'{param_type}_height'] = p.get('height', 0)
                    features[f'{param_type}_width'] = p.get('width', 0)
                    features[f'{param_type}_total_cells'] = p.get('total_cells', 0)
                    features[f'{param_type}_color_count'] = len(p.get('color_palette', []))
        
        # Variations dimensionnelles
        if 'input_differences' in characteristics:
            input_diff = characteristics['input_differences']
            if 'raw_differences' in input_diff and 'dimension_variations' in input_diff['raw_differences']:
                dim_var = input_diff['raw_differences']['dimension_variations']
                all_dims = dim_var.get('all_dimensions', [])
                if all_dims:
                    features['input_dimension_consistency'] = len(set(tuple(d) for d in all_dims)) == 1
        
        if 'output_differences' in characteristics:
            output_diff = characteristics['output_differences']
            if 'raw_differences' in output_diff and 'dimension_variations' in output_diff['raw_differences']:
                dim_var = output_diff['raw_differences']['dimension_variations']
                all_dims = dim_var.get('all_dimensions', [])
                if all_dims:
                    features['output_dimension_consistency'] = len(set(tuple(d) for d in all_dims)) == 1
        
        return features
    
    def analyze_correlations(self):
        """Analyse les corrélations entre caractéristiques et commandes."""
        logger.info("Début de l'analyse des corrélations...")
        
        # Obtenir la liste des tâches
        task_ids = set()
        for file_path in self.results_dir.glob("*_io_differences.json"):
            task_id = file_path.stem.replace('_io_differences', '')
            task_ids.add(task_id)
        
        logger.info(f"Trouvé {len(task_ids)} tâches à analyser")
        
        processed_tasks = 0
        for task_id in sorted(task_ids):
            # Charger les caractéristiques
            characteristics = self.load_task_characteristics(task_id)
            if not characteristics:
                continue
                
            # Charger le scénario
            scenario = self.load_scenario(task_id)
            if not scenario:
                continue
                
            # Extraire les commandes
            commands = self.extract_commands_from_scenario(scenario)
            if not commands:
                continue
                
            # Extraire les caractéristiques clés
            features = self.extract_key_features(characteristics)
            
            # Stocker les données de la tâche
            self.task_data[task_id] = {
                'characteristics': characteristics,
                'scenario': scenario,
                'commands': commands,
                'features': features
            }
            
            # Mettre à jour les statistiques des commandes
            for command in commands:
                self.command_stats[command] += 1
            
            # Calculer les corrélations
            for feature_name, feature_value in features.items():
                for command in commands:
                    # Créer une clé de corrélation
                    if isinstance(feature_value, (int, float)):
                        if feature_value == 0:
                            corr_key = f"{feature_name}=0"
                        elif feature_value > 0:
                            corr_key = f"{feature_name}>0"
                        else:
                            corr_key = f"{feature_name}<0"
                    elif isinstance(feature_value, bool):
                        corr_key = f"{feature_name}={feature_value}"
                    elif isinstance(feature_value, tuple):
                        corr_key = f"{feature_name}={feature_value}"
                    else:
                        corr_key = f"{feature_name}={str(feature_value)}"
                    
                    self.correlations[corr_key][command] += 1
            
            processed_tasks += 1
            if processed_tasks % 50 == 0:
                logger.info(f"Traité {processed_tasks} tâches...")
        
        logger.info(f"Analyse terminée. {processed_tasks} tâches traitées.")
    
    def generate_correlation_report(self) -> Dict[str, Any]:
        """Génère un rapport de corrélations."""
        report = {
            'summary': {
                'total_tasks': len(self.task_data),
                'total_correlations': len(self.correlations),
                'command_frequency': dict(self.command_stats.most_common())
            },
            'strong_correlations': {},
            'feature_command_matrix': {}
        }
        
        # Identifier les corrélations fortes (seuil arbitraire)
        min_occurrences = max(3, len(self.task_data) // 20)  # Au moins 3 ou 5% des tâches
        
        for feature, commands in self.correlations.items():
            total_feature_occurrences = sum(commands.values())
            if total_feature_occurrences >= min_occurrences:
                strong_commands = {}
                for command, count in commands.items():
                    # Calculer le pourcentage de cette commande pour cette caractéristique
                    percentage = (count / total_feature_occurrences) * 100
                    if percentage >= 30:  # Seuil de 30%
                        strong_commands[command] = {
                            'count': count,
                            'percentage': round(percentage, 2),
                            'total_command_uses': self.command_stats[command]
                        }
                
                if strong_commands:
                    report['strong_correlations'][feature] = {
                        'total_occurrences': total_feature_occurrences,
                        'commands': strong_commands
                    }
        
        # Matrice caractéristique-commande
        for feature, commands in self.correlations.items():
            report['feature_command_matrix'][feature] = dict(commands)
        
        return report
    
    def save_results(self, output_dir: str):
        """Sauvegarde les résultats de l'analyse."""
        output_path = Path(output_dir)
        output_path.mkdir(exist_ok=True)
        
        # Rapport de corrélations
        correlation_report = self.generate_correlation_report()
        with open(output_path / 'feature_scenario_correlations.json', 'w', encoding='utf-8') as f:
            json.dump(correlation_report, f, indent=2, ensure_ascii=False)
        
        # Données détaillées des tâches (échantillon)
        sample_tasks = dict(list(self.task_data.items())[:10])  # Premier 10 tâches comme exemple
        with open(output_path / 'sample_task_data.json', 'w', encoding='utf-8') as f:
            json.dump(sample_tasks, f, indent=2, ensure_ascii=False)
        
        # Rapport markdown lisible
        self.generate_markdown_report(output_path / 'analysis_report.md', correlation_report)
        
        logger.info(f"Résultats sauvegardés dans {output_path}")
    
    def generate_markdown_report(self, output_path: Path, correlation_report: Dict[str, Any]):
        """Génère un rapport markdown lisible."""
        with open(output_path, 'w', encoding='utf-8') as f:
            f.write("# Analyse des Correspondances Caractéristiques-Scénarios ARC\n\n")
            
            # Résumé
            summary = correlation_report['summary']
            f.write("## Résumé\n\n")
            f.write(f"- **Tâches analysées**: {summary['total_tasks']}\n")
            f.write(f"- **Corrélations identifiées**: {summary['total_correlations']}\n\n")
            
            # Fréquence des commandes
            f.write("## Fréquence des Commandes AGI\n\n")
            for command, count in summary['command_frequency'].items():
                percentage = (count / summary['total_tasks']) * 100
                f.write(f"- **{command}**: {count} utilisations ({percentage:.1f}%)\n")
            f.write("\n")
            
            # Corrélations fortes
            f.write("## Corrélations Fortes\n\n")
            if correlation_report['strong_correlations']:
                for feature, data in sorted(correlation_report['strong_correlations'].items()):
                    f.write(f"### {feature}\n\n")
                    f.write(f"Occurrences totales: {data['total_occurrences']}\n\n")
                    
                    for command, cmd_data in sorted(data['commands'].items(), 
                                                  key=lambda x: x[1]['percentage'], reverse=True):
                        f.write(f"- **{command}**: {cmd_data['count']} fois ({cmd_data['percentage']}%) ")
                        f.write(f"sur {cmd_data['total_command_uses']} utilisations totales\n")
                    f.write("\n")
            else:
                f.write("Aucune corrélation forte identifiée avec les seuils actuels.\n\n")
            
            # Exemples de tâches
            f.write("## Exemples de Correspondances\n\n")
            example_count = 0
            for task_id, task_data in list(self.task_data.items())[:5]:
                f.write(f"### Tâche {task_id}\n\n")
                f.write(f"**Scénario**: `{task_data['scenario']}`\n\n")
                f.write("**Caractéristiques clés**:\n")
                for feature, value in list(task_data['features'].items())[:5]:
                    f.write(f"- {feature}: {value}\n")
                f.write("\n")
                example_count += 1
                if example_count >= 5:
                    break

def main():
    """Fonction principale."""
    # Chemins des répertoires
    results_dir = "src/grid_analysis/results/training"
    scenarios_dir = "src/grid_analysis/scenarios/training/reduced"
    output_dir = "src/grid_analysis/correlation_analysis"
    
    # Vérifier que les répertoires existent
    if not Path(results_dir).exists():
        logger.error(f"Répertoire des résultats non trouvé: {results_dir}")
        return
    
    if not Path(scenarios_dir).exists():
        logger.error(f"Répertoire des scénarios non trouvé: {scenarios_dir}")
        return
    
    # Créer l'analyseur et lancer l'analyse
    analyzer = FeatureScenarioAnalyzer(results_dir, scenarios_dir)
    analyzer.analyze_correlations()
    analyzer.save_results(output_dir)
    
    logger.info("Analyse terminée avec succès!")

if __name__ == "__main__":
    main()