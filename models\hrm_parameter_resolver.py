"""
HRMParameterResolver pour la résolution algorithmique.

Résolution de paramètres par recherche algorithmique.
Force brute optimisée, pas d'apprentissage automatique.
"""

import numpy as np
import time
import itertools
from typing import Dict, List, Optional, Tuple, Any
from src.command_executor import CommandExecutor
from src.scenario_generalizer import ParameterType

class ParameterAnalyzer:
    """
    Analyseur pour extraire des informations des grilles ARC.
    
    Fournit les fonctions d'analyse nécessaires pour résoudre
    les contraintes des paramètres.
    """
    
    @staticmethod
    def get_most_frequent_color(grid: np.ndarray, exclude_background: bool = True) -> int:
        """Retourne la couleur la plus fréquente (excluant le fond si demandé)."""
        flat_grid = grid.flatten()
        color_counts = Counter(flat_grid)
        
        if exclude_background and 0 in color_counts:
            del color_counts[0]
        
        if not color_counts:
            return 0
        
        return color_counts.most_common(1)[0][0]
    
    @staticmethod
    def get_dominant_color_output(train_pairs: List[Tuple[np.ndarray, np.ndarray]]) -> int:
        """Retourne la couleur dominante dans les outputs des exemples train."""
        all_output_colors = []
        
        for _, output_grid in train_pairs:
            colors = output_grid.flatten()
            all_output_colors.extend(colors[colors != 0])  # Exclure le fond
        
        if not all_output_colors:
            return 1  # Couleur par défaut
        
        return Counter(all_output_colors).most_common(1)[0][0]
    
    @staticmethod
    def detect_geometric_center(grid: np.ndarray) -> Tuple[int, int]:
        """Détecte le centre géométrique de la grille."""
        h, w = grid.shape
        return (h // 2, w // 2)
    
    @staticmethod
    def detect_main_object(grid: np.ndarray) -> List[Tuple[int, int]]:
        """Détecte l'objet principal (plus grande composante connexe non-fond)."""
        # Trouver toutes les positions non-fond
        non_zero_positions = np.argwhere(grid != 0)
        
        if len(non_zero_positions) == 0:
            return []
        
        # Pour simplifier, retourner toutes les positions non-fond
        # Dans une implémentation complète, on ferait une analyse de composantes connexes
        return [(int(pos[0]), int(pos[1])) for pos in non_zero_positions]
    
    @staticmethod
    def detect_pattern_positions(input_grid: np.ndarray, output_grid: np.ndarray) -> List[Tuple[int, int]]:
        """Détecte les positions où des changements ont eu lieu."""
        diff_mask = input_grid != output_grid
        diff_positions = np.argwhere(diff_mask)
        return [(int(pos[0]), int(pos[1])) for pos in diff_positions]
    
    @staticmethod
    def detect_symmetry_axis(grid: np.ndarray) -> str:
        """Détecte l'axe de symétrie principal."""
        h, w = grid.shape
        
        # Test symétrie horizontale
        if np.array_equal(grid, np.flip(grid, axis=1)):
            return "VERTICAL"
        
        # Test symétrie verticale
        if np.array_equal(grid, np.flip(grid, axis=0)):
            return "HORIZONTAL"
        
        return "VERTICAL"  # Par défaut
    
    @staticmethod
    def detect_rotation_angle(input_grid: np.ndarray, output_grid: np.ndarray) -> int:
        """Détecte l'angle de rotation entre input et output."""
        # Test rotations de 90, 180, 270 degrés
        for angle in [90, 180, 270]:
            rotated = np.rot90(input_grid, k=angle//90)
            if np.array_equal(rotated, output_grid):
                return angle
        
        return 0  # Pas de rotation détectée
    
    @staticmethod
    def detect_scale_factor(input_grid: np.ndarray, output_grid: np.ndarray) -> int:
        """Détecte le facteur d'échelle entre input et output."""
        input_objects = len(np.argwhere(input_grid != 0))
        output_objects = len(np.argwhere(output_grid != 0))
        
        if input_objects == 0:
            return 1
        
        scale = output_objects // input_objects
        return max(1, scale)
    
    @staticmethod
    def detect_translation_vector(input_grid: np.ndarray, output_grid: np.ndarray) -> Tuple[int, int]:
        """Détecte le vecteur de translation."""
        # Simplification : détecter le décalage du centre de masse
        input_positions = np.argwhere(input_grid != 0)
        output_positions = np.argwhere(output_grid != 0)
        
        if len(input_positions) == 0 or len(output_positions) == 0:
            return (0, 0)
        
        input_center = np.mean(input_positions, axis=0)
        output_center = np.mean(output_positions, axis=0)
        
        translation = output_center - input_center
        return (int(translation[0]), int(translation[1]))

class ResolvedParameter:
    """Paramètre résolu avec sa valeur concrète."""
    param_name: str
    param_type: ParameterType
    resolved_value: Any
    confidence: float = 1.0

class HRMParameterResolver:
    """
    Résolution de paramètres par recherche algorithmique.
    Force brute optimisée, pas d'apprentissage automatique.
    """
    
    def __init__(self, timeout_seconds: int = 10):
        """Initialise le résolveur avec timeout et exécuteur de commandes."""
        self.timeout_seconds = timeout_seconds
        self.command_executor = CommandExecutor()
        
    def resolve_parameters(self, template: str, test_input: np.ndarray, 
                          train_examples: List) -> Optional[str]:
        """
        Algorithme de résolution :
        1. Énumérer valeurs possibles pour chaque variable
        2. Tester combinaisons sur train examples
        3. Sélectionner meilleure combinaison (score)
        4. Appliquer au test_input
        """
        start_time = time.time()
        
        # 1. Énumérer valeurs possibles pour chaque variable
        parameter_values = self._enumerate_parameter_values(template, train_examples)
        
        if not parameter_values:
            return None
        
        # 2. Tester combinaisons sur train examples
        best_scenario = None
        best_score = 0.0
        
        # Générer toutes les combinaisons possibles (force brute optimisée)
        param_names = list(parameter_values.keys())
        param_combinations = itertools.product(*[parameter_values[name] for name in param_names])
        
        combinations_tested = 0
        for combination in param_combinations:
            # Vérifier le timeout
            if time.time() - start_time > self.timeout_seconds:
                print(f"Timeout dépassé ({self.timeout_seconds}s) après {combinations_tested} combinaisons")
                break
            
            # Créer le scénario avec cette combinaison
            scenario = self._instantiate_template(template, param_names, combination)
            
            # 3. Tester sur les exemples train
            score = self.validate_solution(scenario, train_examples)
            combinations_tested += 1
            
            if score > best_score:
                best_score = score
                best_scenario = scenario
                
                # Si score parfait, arrêter la recherche (early stopping)
                if score >= 1.0:
                    print(f"Solution parfaite trouvée après {combinations_tested} combinaisons")
                    break
        
        print(f"Recherche terminée: {combinations_tested} combinaisons testées, meilleur score: {best_score:.2f}")
        
        # Pour la démonstration, retourner le meilleur scénario même avec score faible
        # Dans un vrai système, on utiliserait un seuil plus strict
        if best_scenario is not None:
            return best_scenario
        
        # Fallback: retourner le template avec des valeurs par défaut
        if parameter_values:
            param_names = list(parameter_values.keys())
            default_combination = tuple(parameter_values[name][0] if parameter_values[name] else "1"
                                      for name in param_names)
            fallback_scenario = self._instantiate_template(template, param_names, default_combination)
            print(f"Utilisation du scénario de fallback: {fallback_scenario}")
            return fallback_scenario
        
        return None
    
    def validate_solution(self, scenario: str, train_examples: List) -> float:
        """
        Validation par exécution :
        - Exécuter scenario sur chaque train input
        - Comparer avec train output attendu
        - Calculer score de correspondance (0.0 à 1.0)
        """
        if not train_examples:
            return 0.0
        
        successful_executions = 0
        total_examples = len(train_examples)
        
        for input_grid, expected_output in train_examples:
            try:
                # Exécuter le scénario sur cet exemple
                result = self._execute_scenario_on_grid(scenario, input_grid)
                
                if result is not None and np.array_equal(result, expected_output):
                    successful_executions += 1
                    
            except Exception:
                continue  # Échec d'exécution pour cet exemple
        
        return successful_executions / total_examples
    
    def _enumerate_parameter_values(self, template: str, train_examples: List) -> Dict[str, List]:
        """Énumération des valeurs possibles pour chaque paramètre"""
        import re
        
        # Extraire les variables du template (éviter les accolades de structure MOTIF)
        # Chercher seulement les variables avec des noms valides (lettres, chiffres, underscore)
        variables = re.findall(r'\{([a-zA-Z_][a-zA-Z0-9_]*)\}', template)
        parameter_values = {}
        
        for var in variables:
            if 'color' in var.lower():
                parameter_values[var] = self._get_possible_colors(train_examples)
            elif 'angle' in var.lower():
                # Conformité grammaire : ROTATE ("LEFT" | "RIGHT")
                parameter_values[var] = ["LEFT", "RIGHT"]
            elif 'center' in var.lower():
                parameter_values[var] = self._get_possible_centers(train_examples)
            elif 'size' in var.lower() or 'width' in var.lower() or 'height' in var.lower():
                parameter_values[var] = self._get_possible_sizes(train_examples)
            elif 'region' in var.lower() or 'area' in var.lower() or 'positions' in var.lower():
                parameter_values[var] = self._get_possible_regions(train_examples)
            elif 'axis' in var.lower():
                # Conformité grammaire : FLIP ("HORIZONTAL" | "VERTICAL")
                parameter_values[var] = ["HORIZONTAL", "VERTICAL"]
            elif 'factor' in var.lower():
                parameter_values[var] = ["2", "3", "4"]  # STRING pour conformité
            elif 'method' in var.lower():
                parameter_values[var] = ["nearest"]
            else:
                parameter_values[var] = ["1", "2", "3"]  # Valeurs par défaut
        
        return parameter_values

    def _instantiate_template(self, template: str, param_names: List[str], combination: tuple) -> str:
        """
        Instancie un template avec des valeurs de paramètres.
        Assure la conformité avec la grammaire ARCgrammarv9.lark.
        """
        scenario = template

        # Debug: afficher le template original
        # print(f"DEBUG: Template original: {template}")
        # print(f"DEBUG: Paramètres: {param_names}")
        # print(f"DEBUG: Valeurs: {combination}")

        for i, param_name in enumerate(param_names):
            value = str(combination[i])
            placeholder = f"{{{param_name}}}"

            # Formatage spécial selon le type de paramètre
            if 'width' in param_name.lower() or 'height' in param_name.lower():
                # Pour les dimensions dans les rectangles, s'assurer que c'est un entier
                if 'x' in value:
                    # Si c'est un GRID_SIZE, extraire la première dimension
                    value = value.split('x')[0]
                scenario = scenario.replace(placeholder, value)
            elif 'size' in param_name.lower():
                # Pour GRID_SIZE, s'assurer du format INTxINT
                if 'x' not in value:
                    scenario = scenario.replace(placeholder, f"{value}x{value}")
                else:
                    scenario = scenario.replace(placeholder, value)
            else:
                # Autres paramètres : utilisation directe
                scenario = scenario.replace(placeholder, value)

            # Debug: afficher après chaque substitution
            # print(f"DEBUG: Après substitution {placeholder} -> {value}: {scenario}")

        return scenario

    def _test_parameter_combination(self, scenario: str, train_examples: List) -> bool:
        """Test d'une combinaison de paramètres sur tous les exemples"""
        return self.validate_solution(scenario, train_examples) >= 0.5
    
    def _get_possible_colors(self, train_examples: List) -> List[str]:
        """Extrait les couleurs possibles des exemples"""
        colors = set()
        for input_grid, output_grid in train_examples:
            colors.update(np.unique(input_grid))
            colors.update(np.unique(output_grid))
        # Exclure le fond (0) et limiter à 5 couleurs pour éviter l'explosion combinatoire
        return [str(c) for c in sorted(colors) if c != 0][:5]
    
    def _get_possible_centers(self, train_examples: List) -> List[str]:
        """Extrait les centres possibles - Conformité grammaire : cell format [INT,INT]"""
        centers = []
        for input_grid, _ in train_examples:
            h, w = input_grid.shape
            # Conformité grammaire : cell: "[" INT "," INT "]"
            centers.append(f"[{h//2},{w//2}]")
        return list(set(centers))
    
    def _get_possible_sizes(self, train_examples: List) -> List[str]:
        """Extrait les tailles possibles - Conformité grammaire : GRID_SIZE format INTxINT"""
        sizes = []
        dimensions = []
        for input_grid, output_grid in train_examples:
            h1, w1 = input_grid.shape
            h2, w2 = output_grid.shape
            # Pour GRID_SIZE (RESIZE) : format INTxINT
            sizes.extend([f"{w1}x{h1}", f"{w2}x{h2}"])
            # Pour width/height dans les rectangles : dimensions individuelles (0-based)
            dimensions.extend([str(w1-1), str(h1-1), str(w2-1), str(h2-1)])

        # Retourner les tailles pour GRID_SIZE et les dimensions pour rectangles
        all_values = list(set(sizes + dimensions))
        return all_values[:8]  # Limiter pour éviter l'explosion combinatoire
    
    def _get_possible_regions(self, train_examples: List) -> List[str]:
        """Extrait les régions possibles - Conformité grammaire : rectangle format [INT,INT INT,INT]"""
        regions = []
        for input_grid, _ in train_examples:
            h, w = input_grid.shape
            # Conformité grammaire : rectangle: "[" INT "," INT " " INT "," INT "]"
            regions.extend([
                f"[0,0 {w-1},{h-1}]",  # Grille complète (indices 0-based)
                f"[0,0 {w//2},{h//2}]",  # Quart supérieur gauche
                f"[{w//2},{h//2} {w-1},{h-1}]",  # Quart inférieur droit
                f"[0,0]",  # Cellule origine (pour PASTE)
                f"[{w//2},{h//2}]"  # Cellule centre
            ])
        # Limiter pour éviter l'explosion combinatoire
        return list(set(regions))[:5]
    
    def _execute_scenario_on_grid(self, scenario: str, input_grid: np.ndarray) -> Optional[np.ndarray]:
        """Exécute un scénario sur une grille"""
        try:
            h, w = input_grid.shape
            # Conformité grammaire : INIT GRID_SIZE où GRID_SIZE: INT "x" INT
            commands = [f"INIT {w}x{h}", scenario]
            
            # Créer un nouvel exécuteur pour éviter les interférences
            executor = CommandExecutor()
            executor.grid = input_grid.copy()
            executor.width = w
            executor.height = h
            
            result_dict = executor.execute_commands(commands)
            
            if result_dict["success"] and result_dict["grid"] is not None:
                return np.array(result_dict["grid"])
            else:
                return None
                
        except Exception:
            return None
    
    def get_resolver_stats(self) -> Dict[str, Any]:
        """Retourne des statistiques sur le résolveur (pour debugging)"""
        return {
            'method': 'algorithmic_search',
            'timeout_seconds': self.timeout_seconds,
            'search_strategy': 'brute_force_optimized',
            'early_stopping': True,
            'note': 'Recherche algorithmique par force brute, pas d\'apprentissage automatique'
        }