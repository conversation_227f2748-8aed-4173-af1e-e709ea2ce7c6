#!/usr/bin/env python3
"""
Vue d'ensemble de l'analyse des correspondances caractéristiques-scénarios ARC.

Ce script génère un aperçu complet de tous les fichiers et résultats produits
par l'analyse des correspondances entre les caractéristiques des puzzles ARC
et les commandes de résolution.
"""

import os
from pathlib import Path
from typing import Dict, List
import logging

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def generate_analysis_overview():
    """Génère un aperçu complet de l'analyse."""
    
    base_dir = Path("src/grid_analysis")
    
    overview = {
        'title': 'Vue d\'ensemble de l\'Analyse des Correspondances ARC',
        'description': 'Analyse complète des correspondances entre les caractéristiques des puzzles ARC et les scénarios de résolution',
        'directories': {},
        'key_files': {},
        'summary': {}
    }
    
    # Analyser les répertoires et fichiers
    directories_to_analyze = [
        'results/training',
        'scenarios/training/reduced', 
        'correlation_analysis',
        'synthesis_results',
        'executive_summary'
    ]
    
    for dir_name in directories_to_analyze:
        dir_path = base_dir / dir_name
        if dir_path.exists():
            files = list(dir_path.glob('*'))
            overview['directories'][dir_name] = {
                'path': str(dir_path),
                'file_count': len(files),
                'files': [f.name for f in files[:5]]  # Premier 5 fichiers comme exemple
            }
    
    # Fichiers clés avec descriptions
    key_files = {
        'feature_scenario_analyzer.py': 'Script principal d\'analyse des correspondances',
        'correlation_synthesizer.py': 'Synthétiseur de règles de correspondance',
        'executive_summary.py': 'Générateur de résumé exécutif',
        'correlation_analysis/feature_scenario_correlations.json': 'Données de corrélation brutes',
        'correlation_analysis/analysis_report.md': 'Rapport d\'analyse initial',
        'synthesis_results/comprehensive_analysis.json': 'Analyse complète avec règles',
        'synthesis_results/transformation_rules.json': 'Règles de transformation extraites',
        'synthesis_results/detailed_analysis_report.md': 'Rapport détaillé de synthèse',
        'executive_summary/executive_summary.md': 'Résumé exécutif final',
        'executive_summary/transformation_matrix.json': 'Matrice de transformation pour usage programmatique'
    }
    
    for file_path, description in key_files.items():
        full_path = base_dir / file_path
        overview['key_files'][file_path] = {
            'description': description,
            'exists': full_path.exists(),
            'size_kb': round(full_path.stat().st_size / 1024, 2) if full_path.exists() else 0
        }
    
    # Résumé des résultats
    overview['summary'] = {
        'total_tasks_analyzed': 318,
        'correlations_identified': 26,
        'transformation_rules_generated': 19,
        'key_insights': [
            '95.6% des solutions utilisent MOTIF+PASTE',
            '47.2% incluent de l\'édition directe (EDIT)',
            '38.7% utilisent des opérations de découpe (CUT)',
            'Taille de grille la plus fréquente: 9x9 (107 occurrences)',
            'Hiérarchie claire des commandes: Core (MOTIF, PASTE) → Common (EDIT) → Specialized (CUT, FILL, COLOR)'
        ]
    }
    
    return overview

def save_overview_report(overview: Dict, output_path: str):
    """Sauvegarde le rapport d'aperçu."""
    output_file = Path(output_path)
    
    with open(output_file, 'w', encoding='utf-8') as f:
        f.write(f"# {overview['title']}\n\n")
        f.write(f"{overview['description']}\n\n")
        
        # Résumé des résultats
        f.write("## 📊 Résumé des Résultats\n\n")
        summary = overview['summary']
        f.write(f"- **Tâches analysées**: {summary['total_tasks_analyzed']}\n")
        f.write(f"- **Corrélations identifiées**: {summary['correlations_identified']}\n")
        f.write(f"- **Règles de transformation générées**: {summary['transformation_rules_generated']}\n\n")
        
        f.write("### Insights Clés\n\n")
        for insight in summary['key_insights']:
            f.write(f"- {insight}\n")
        f.write("\n")
        
        # Structure des répertoires
        f.write("## 📁 Structure des Répertoires\n\n")
        for dir_name, dir_info in overview['directories'].items():
            f.write(f"### {dir_name}\n\n")
            f.write(f"- **Chemin**: `{dir_info['path']}`\n")
            f.write(f"- **Nombre de fichiers**: {dir_info['file_count']}\n")
            if dir_info['files']:
                f.write(f"- **Exemples de fichiers**: {', '.join(dir_info['files'])}\n")
            f.write("\n")
        
        # Fichiers clés
        f.write("## 🔑 Fichiers Clés\n\n")
        for file_path, file_info in overview['key_files'].items():
            status = "✅" if file_info['exists'] else "❌"
            f.write(f"### {file_path} {status}\n\n")
            f.write(f"- **Description**: {file_info['description']}\n")
            if file_info['exists']:
                f.write(f"- **Taille**: {file_info['size_kb']} KB\n")
            f.write("\n")
        
        # Guide d'utilisation
        f.write("## 🚀 Guide d'Utilisation\n\n")
        f.write("### Pour reproduire l'analyse complète:\n\n")
        f.write("```bash\n")
        f.write("# 1. Analyse des correspondances\n")
        f.write("python src/grid_analysis/feature_scenario_analyzer.py\n\n")
        f.write("# 2. Synthèse des règles\n")
        f.write("python src/grid_analysis/correlation_synthesizer.py\n\n")
        f.write("# 3. Génération du résumé exécutif\n")
        f.write("python src/grid_analysis/executive_summary.py\n")
        f.write("```\n\n")
        
        f.write("### Pour consulter les résultats:\n\n")
        f.write("- **Résumé exécutif**: `src/grid_analysis/executive_summary/executive_summary.md`\n")
        f.write("- **Analyse détaillée**: `src/grid_analysis/synthesis_results/detailed_analysis_report.md`\n")
        f.write("- **Règles de transformation**: `src/grid_analysis/synthesis_results/transformation_rules.json`\n")
        f.write("- **Matrice de transformation**: `src/grid_analysis/executive_summary/transformation_matrix.json`\n\n")
        
        # Applications pratiques
        f.write("## 💡 Applications Pratiques\n\n")
        f.write("### Pour les développeurs de modèles IA:\n\n")
        f.write("- Utiliser les règles de transformation pour guider l'architecture du modèle\n")
        f.write("- Prioriser l'entraînement sur les commandes core (MOTIF, PASTE)\n")
        f.write("- Intégrer la détection des changements dimensionnels\n\n")
        
        f.write("### Pour les chercheurs:\n\n")
        f.write("- Analyser les patterns de résolution humaine vs IA\n")
        f.write("- Développer de nouvelles métriques basées sur les correspondances\n")
        f.write("- Étendre l'analyse à d'autres domaines de puzzles\n\n")
        
        f.write("### Pour l'implémentation:\n\n")
        f.write("- Intégrer la matrice de transformation dans les systèmes de résolution\n")
        f.write("- Développer un classificateur de puzzles basé sur les caractéristiques\n")
        f.write("- Créer un système de recommandation de stratégies\n\n")
        
        # Conclusion
        f.write("## 📝 Conclusion\n\n")
        f.write("Cette analyse complète révèle des patterns clairs et actionnables dans la résolution ")
        f.write("des puzzles ARC. Les correspondances identifiées entre les caractéristiques des puzzles ")
        f.write("et les commandes de résolution fournissent une base solide pour améliorer les systèmes ")
        f.write("d'IA et développer de nouvelles approches de résolution automatique.\n\n")
        
        f.write("Les règles de transformation générées peuvent être directement intégrées dans les ")
        f.write("modèles existants, tandis que les insights stratégiques guident le développement ")
        f.write("de futures architectures plus efficaces.\n")

def main():
    """Fonction principale."""
    overview = generate_analysis_overview()
    output_path = "src/grid_analysis/ANALYSIS_OVERVIEW.md"
    
    save_overview_report(overview, output_path)
    
    logger.info(f"Aperçu de l'analyse sauvegardé dans {output_path}")
    
    # Afficher un résumé dans la console
    print("\n" + "="*60)
    print("ANALYSE DES CORRESPONDANCES ARC - RÉSUMÉ")
    print("="*60)
    print(f"📊 Tâches analysées: {overview['summary']['total_tasks_analyzed']}")
    print(f"🔗 Corrélations identifiées: {overview['summary']['correlations_identified']}")
    print(f"📋 Règles générées: {overview['summary']['transformation_rules_generated']}")
    print("\n🎯 Insights principaux:")
    for insight in overview['summary']['key_insights'][:3]:
        print(f"   • {insight}")
    print(f"\n📁 Fichiers générés: {len([f for f in overview['key_files'].values() if f['exists']])}")
    print(f"📖 Rapport complet: {output_path}")
    print("="*60)

if __name__ == "__main__":
    main()