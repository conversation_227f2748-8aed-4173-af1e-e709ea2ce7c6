"""
Analyse des différences entre les grilles INPUT des exemples train.
Hiérarchie : <PERSON><PERSON><PERSON> → <PERSON><PERSON><PERSON> → Notable
"""

import numpy as np
from typing import Dict, List, Any, Tuple
from collections import Counter
try:
    from .grid_parameters import GridParameters
except ImportError:
    # Import direct si exécuté comme script
    from grid_parameters import GridParameters


class InputDifferences:
    """
    Analyse des différences entre les grilles INPUT des exemples train.
    
    Niveau 1 : Différences brutes (calculs directs)
    Niveau 2 : Analyses (patterns, tendances)  
    Niveau 3 : Notable (ce qui ressort, insights)
    """
    
    def __init__(self, input_grids: List[np.ndarray]):
        """Initialise avec la liste des grilles input des exemples train."""
        self.input_grids = input_grids
        self.grid_params = [GridParameters(grid) for grid in input_grids]
        
        # Calculs des 3 niveaux
        self.raw_differences = self._compute_raw_differences()
        self.analysis = self._compute_analysis()
        self.notable = self._compute_notable()
    
    def _compute_raw_differences(self) -> Dict[str, Any]:
        """Niveau 1 : Différences brutes entre les inputs."""
        if len(self.input_grids) < 2:
            return {"error": "Moins de 2 grilles input pour comparer"}
        
        raw = {
            "grid_count": len(self.input_grids),
            "dimension_variations": self._get_dimension_variations(),
            "color_variations": self._get_color_variations(),
            "size_variations": self._get_size_variations(),
            "density_variations": self._get_density_variations(),
            "pairwise_differences": self._get_pairwise_differences()
        }
        
        return raw
    
    def _get_dimension_variations(self) -> Dict[str, Any]:
        """Variations dimensionnelles entre inputs."""
        dimensions = [(grid.shape[1], grid.shape[0]) for grid in self.input_grids]  # (width, height)
        
        return {
            "all_dimensions": dimensions,
            "unique_dimensions": list(set(dimensions)),
            "dimension_count": len(set(dimensions)),
            "same_dimensions": len(set(dimensions)) == 1,
            "min_size": min(w * h for w, h in dimensions),
            "max_size": max(w * h for w, h in dimensions),
            "size_range": max(w * h for w, h in dimensions) - min(w * h for w, h in dimensions)
        }
    
    def _get_color_variations(self) -> Dict[str, Any]:
        """Variations chromatiques entre inputs."""
        all_colors = set()
        color_counts = []
        dominant_colors = []
        
        for params in self.grid_params:
            all_colors.update(params.color_palette)
            color_counts.append(params.unique_colors_count)
            dominant_colors.append(params.dominant_color)
        
        return {
            "global_palette": sorted(list(all_colors)),
            "global_palette_size": len(all_colors),
            "color_counts_per_grid": color_counts,
            "min_colors": min(color_counts),
            "max_colors": max(color_counts),
            "color_count_range": max(color_counts) - min(color_counts),
            "dominant_colors": dominant_colors,
            "same_dominant_color": len(set(dominant_colors)) == 1,
            "dominant_color_distribution": dict(Counter(dominant_colors))
        }
    
    def _get_size_variations(self) -> Dict[str, Any]:
        """Variations de taille entre inputs."""
        total_cells = [params.total_cells for params in self.grid_params]
        
        return {
            "total_cells_per_grid": total_cells,
            "min_total_cells": min(total_cells),
            "max_total_cells": max(total_cells),
            "total_cells_range": max(total_cells) - min(total_cells),
            "same_total_cells": len(set(total_cells)) == 1
        }
    
    def _get_density_variations(self) -> Dict[str, Any]:
        """Variations de densité colorée entre inputs."""
        densities = [params.colored_density for params in self.grid_params]
        
        return {
            "densities": densities,
            "min_density": min(densities),
            "max_density": max(densities),
            "density_range": max(densities) - min(densities),
            "average_density": sum(densities) / len(densities)
        }
    
    def _get_pairwise_differences(self) -> List[Dict[str, Any]]:
        """Différences par paires entre grilles."""
        differences = []
        
        for i in range(len(self.input_grids)):
            for j in range(i + 1, len(self.input_grids)):
                grid1, grid2 = self.input_grids[i], self.input_grids[j]
                params1, params2 = self.grid_params[i], self.grid_params[j]
                
                diff = {
                    "pair": (i, j),
                    "dimension_change": (grid2.shape[1] - grid1.shape[1], grid2.shape[0] - grid1.shape[0]),
                    "size_change": params2.total_cells - params1.total_cells,
                    "color_count_change": params2.unique_colors_count - params1.unique_colors_count,
                    "density_change": params2.colored_density - params1.colored_density,
                    "dominant_color_change": params1.dominant_color != params2.dominant_color,
                    "shared_colors": len(set(params1.color_palette) & set(params2.color_palette)),
                    "unique_to_first": list(set(params1.color_palette) - set(params2.color_palette)),
                    "unique_to_second": list(set(params2.color_palette) - set(params1.color_palette))
                }
                
                differences.append(diff)
        
        return differences
    
    def _compute_analysis(self) -> Dict[str, Any]:
        """Niveau 2 : Analyse des patterns dans les différences."""
        raw = self.raw_differences
        
        if "error" in raw:
            return {"error": raw["error"]}
        
        analysis = {
            "dimension_patterns": self._analyze_dimension_patterns(),
            "color_patterns": self._analyze_color_patterns(),
            "progression_patterns": self._analyze_progression_patterns(),
            "consistency_analysis": self._analyze_consistency()
        }
        
        return analysis
    
    def _analyze_dimension_patterns(self) -> Dict[str, Any]:
        """Analyse des patterns dimensionnels."""
        dim_vars = self.raw_differences["dimension_variations"]
        
        patterns = {
            "is_uniform_dimensions": dim_vars["same_dimensions"],
            "dimension_progression": None,
            "size_progression": None
        }
        
        if len(dim_vars["all_dimensions"]) > 2:
            # Analyser la progression des dimensions
            widths = [d[0] for d in dim_vars["all_dimensions"]]
            heights = [d[1] for d in dim_vars["all_dimensions"]]
            
            # Progression arithmétique ?
            width_diffs = [widths[i+1] - widths[i] for i in range(len(widths)-1)]
            height_diffs = [heights[i+1] - heights[i] for i in range(len(heights)-1)]
            
            patterns["dimension_progression"] = {
                "width_arithmetic": len(set(width_diffs)) == 1 if width_diffs else False,
                "height_arithmetic": len(set(height_diffs)) == 1 if height_diffs else False,
                "width_step": width_diffs[0] if width_diffs and len(set(width_diffs)) == 1 else None,
                "height_step": height_diffs[0] if height_diffs and len(set(height_diffs)) == 1 else None
            }
        
        return patterns
    
    def _analyze_color_patterns(self) -> Dict[str, Any]:
        """Analyse des patterns chromatiques."""
        color_vars = self.raw_differences["color_variations"]
        
        patterns = {
            "uniform_palette_size": len(set(color_vars["color_counts_per_grid"])) == 1,
            "palette_progression": None,
            "dominant_color_stability": color_vars["same_dominant_color"],
            "color_introduction_pattern": self._analyze_color_introduction()
        }
        
        # Progression du nombre de couleurs
        counts = color_vars["color_counts_per_grid"]
        if len(counts) > 2:
            count_diffs = [counts[i+1] - counts[i] for i in range(len(counts)-1)]
            patterns["palette_progression"] = {
                "arithmetic": len(set(count_diffs)) == 1 if count_diffs else False,
                "step": count_diffs[0] if count_diffs and len(set(count_diffs)) == 1 else None,
                "monotonic_increase": all(d >= 0 for d in count_diffs),
                "monotonic_decrease": all(d <= 0 for d in count_diffs)
            }
        
        return patterns
    
    def _analyze_color_introduction(self) -> Dict[str, Any]:
        """Analyse comment les couleurs sont introduites entre les grilles."""
        introduction = {
            "new_colors_per_step": [],
            "cumulative_palette": []
        }
        
        seen_colors = set()
        for i, params in enumerate(self.grid_params):
            current_colors = set(params.color_palette)
            new_colors = current_colors - seen_colors
            
            introduction["new_colors_per_step"].append(list(new_colors))
            seen_colors.update(current_colors)
            introduction["cumulative_palette"].append(list(seen_colors))
        
        return introduction
    
    def _analyze_progression_patterns(self) -> Dict[str, Any]:
        """Analyse des progressions générales."""
        densities = [params.colored_density for params in self.grid_params]
        sizes = [params.total_cells for params in self.grid_params]
        
        patterns = {
            "density_trend": self._get_trend(densities),
            "size_trend": self._get_trend(sizes),
            "complexity_trend": self._analyze_complexity_trend()
        }
        
        return patterns
    
    def _get_trend(self, values: List[float]) -> str:
        """Détermine la tendance d'une série de valeurs."""
        if len(values) < 2:
            return "insufficient_data"
        
        diffs = [values[i+1] - values[i] for i in range(len(values)-1)]
        
        if all(d > 0 for d in diffs):
            return "increasing"
        elif all(d < 0 for d in diffs):
            return "decreasing"
        elif all(d == 0 for d in diffs):
            return "constant"
        else:
            return "variable"
    
    def _analyze_complexity_trend(self) -> Dict[str, Any]:
        """Analyse la tendance de complexité (couleurs × taille)."""
        complexities = [params.unique_colors_count * params.total_cells for params in self.grid_params]
        
        return {
            "values": complexities,
            "trend": self._get_trend(complexities),
            "range": max(complexities) - min(complexities) if complexities else 0
        }
    
    def _analyze_consistency(self) -> Dict[str, Any]:
        """Analyse la cohérence entre les grilles input."""
        pairwise = self.raw_differences["pairwise_differences"]
        
        consistency = {
            "dimension_consistency": self._check_dimension_consistency(pairwise),
            "color_consistency": self._check_color_consistency(pairwise),
            "overall_consistency_score": 0.0
        }
        
        # Score de cohérence global (0-1)
        scores = []
        if consistency["dimension_consistency"]["score"] is not None:
            scores.append(consistency["dimension_consistency"]["score"])
        if consistency["color_consistency"]["score"] is not None:
            scores.append(consistency["color_consistency"]["score"])
        
        consistency["overall_consistency_score"] = sum(scores) / len(scores) if scores else 0.0
        
        return consistency
    
    def _check_dimension_consistency(self, pairwise: List[Dict]) -> Dict[str, Any]:
        """Vérifie la cohérence dimensionnelle."""
        if not pairwise:
            return {"score": None, "pattern": "no_pairs"}
        
        dimension_changes = [pair["dimension_change"] for pair in pairwise]
        unique_changes = set(dimension_changes)
        
        return {
            "score": 1.0 if len(unique_changes) == 1 else 0.5 if len(unique_changes) <= len(pairwise) // 2 else 0.0,
            "pattern": "uniform" if len(unique_changes) == 1 else "variable",
            "unique_changes": list(unique_changes)
        }
    
    def _check_color_consistency(self, pairwise: List[Dict]) -> Dict[str, Any]:
        """Vérifie la cohérence chromatique."""
        if not pairwise:
            return {"score": None, "pattern": "no_pairs"}
        
        color_changes = [pair["color_count_change"] for pair in pairwise]
        unique_changes = set(color_changes)
        
        return {
            "score": 1.0 if len(unique_changes) == 1 else 0.5 if len(unique_changes) <= len(pairwise) // 2 else 0.0,
            "pattern": "uniform" if len(unique_changes) == 1 else "variable",
            "unique_changes": list(unique_changes)
        }
    
    def _compute_notable(self) -> Dict[str, Any]:
        """Niveau 3 : Ce qui est notable, les insights importants."""
        if "error" in self.raw_differences:
            return {"error": self.raw_differences["error"]}
        
        notable = {
            "key_insights": [],
            "anomalies": [],
            "strong_patterns": [],
            "recommendations": [],
            "confidence_score": 0.0
        }
        
        # Identifier les insights clés
        notable["key_insights"] = self._identify_key_insights()
        notable["anomalies"] = self._identify_anomalies()
        notable["strong_patterns"] = self._identify_strong_patterns()
        notable["recommendations"] = self._generate_recommendations()
        
        # Score de confiance global
        notable["confidence_score"] = self._calculate_confidence_score()
        
        return notable
    
    def _identify_key_insights(self) -> List[str]:
        """Identifie les insights clés sur les inputs."""
        insights = []
        
        raw = self.raw_differences
        analysis = self.analysis
        
        # Insights dimensionnels
        if raw["dimension_variations"]["same_dimensions"]:
            insights.append(f"Toutes les grilles input ont les mêmes dimensions ({raw['dimension_variations']['unique_dimensions'][0]})")
        elif raw["dimension_variations"]["dimension_count"] == len(self.input_grids):
            insights.append("Chaque grille input a des dimensions uniques")
        
        # Insights chromatiques
        if analysis["color_patterns"]["uniform_palette_size"]:
            insights.append(f"Toutes les grilles input utilisent exactement {raw['color_variations']['color_counts_per_grid'][0]} couleurs")
        
        if analysis["color_patterns"]["dominant_color_stability"]:
            insights.append(f"La couleur dominante est stable : {raw['color_variations']['dominant_colors'][0]}")
        
        # Insights de progression
        if analysis["progression_patterns"]["size_trend"] == "increasing":
            insights.append("Les grilles input augmentent progressivement en taille")
        elif analysis["progression_patterns"]["size_trend"] == "decreasing":
            insights.append("Les grilles input diminuent progressivement en taille")
        
        return insights
    
    def _identify_anomalies(self) -> List[str]:
        """Identifie les anomalies dans les inputs."""
        anomalies = []
        
        raw = self.raw_differences
        
        # Anomalies de taille
        if raw["size_variations"]["total_cells_range"] > raw["size_variations"]["max_total_cells"] * 0.5:
            anomalies.append("Écart de taille très important entre les grilles input")
        
        # Anomalies chromatiques
        if raw["color_variations"]["color_count_range"] > 3:
            anomalies.append("Variation importante du nombre de couleurs entre inputs")
        
        # Anomalies de densité
        if raw["density_variations"]["density_range"] > 0.7:
            anomalies.append("Variation extrême de densité colorée entre inputs")
        
        return anomalies
    
    def _identify_strong_patterns(self) -> List[str]:
        """Identifie les patterns forts dans les inputs."""
        patterns = []
        
        analysis = self.analysis
        
        # Patterns dimensionnels forts
        if analysis["dimension_patterns"]["dimension_progression"]:
            prog = analysis["dimension_patterns"]["dimension_progression"]
            if prog["width_arithmetic"] and prog["height_arithmetic"]:
                patterns.append(f"Progression arithmétique des dimensions : +{prog['width_step']}×{prog['height_step']} par étape")
        
        # Patterns chromatiques forts
        if analysis["color_patterns"]["palette_progression"]:
            prog = analysis["color_patterns"]["palette_progression"]
            if prog["arithmetic"]:
                patterns.append(f"Progression arithmétique du nombre de couleurs : {prog['step']:+d} par étape")
        
        # Cohérence forte
        if analysis["consistency_analysis"]["overall_consistency_score"] > 0.8:
            patterns.append("Très forte cohérence entre les grilles input")
        
        return patterns
    
    def _generate_recommendations(self) -> List[str]:
        """Génère des recommandations d'analyse."""
        recommendations = []
        
        analysis = self.analysis
        
        if analysis["consistency_analysis"]["overall_consistency_score"] > 0.7:
            recommendations.append("Les inputs sont cohérents - chercher des patterns de transformation réguliers")
        else:
            recommendations.append("Les inputs sont variables - analyser chaque transformation individuellement")
        
        if len(self.raw_differences["color_variations"]["global_palette"]) <= 3:
            recommendations.append("Palette limitée - les transformations pourraient être basées sur les positions")
        else:
            recommendations.append("Palette riche - les transformations pourraient être basées sur les couleurs")
        
        return recommendations
    
    def _calculate_confidence_score(self) -> float:
        """Calcule un score de confiance pour l'analyse."""
        if len(self.input_grids) < 2:
            return 0.0
        
        scores = []
        
        # Score basé sur la cohérence
        consistency_score = self.analysis["consistency_analysis"]["overall_consistency_score"]
        scores.append(consistency_score)
        
        # Score basé sur le nombre d'exemples
        sample_score = min(1.0, len(self.input_grids) / 5.0)  # Optimal à 5+ exemples
        scores.append(sample_score)
        
        # Score basé sur la diversité (pas trop uniforme, pas trop chaotique)
        diversity_score = 0.5  # Score neutre par défaut
        if self.raw_differences["dimension_variations"]["dimension_count"] > 1:
            diversity_score += 0.2
        if self.raw_differences["color_variations"]["color_count_range"] > 0:
            diversity_score += 0.2
        diversity_score = min(1.0, diversity_score)
        scores.append(diversity_score)
        
        return sum(scores) / len(scores)
    
    def _convert_to_json_serializable(self, obj):
        """Convertit les types NumPy en types Python natifs pour JSON."""
        if isinstance(obj, np.integer):
            return int(obj)
        elif isinstance(obj, np.floating):
            return float(obj)
        elif isinstance(obj, (np.bool_, bool)):
            return bool(obj)
        elif isinstance(obj, np.ndarray):
            return obj.tolist()
        elif isinstance(obj, dict):
            # Convertir les clés ET les valeurs
            converted_dict = {}
            for k, v in obj.items():
                # Convertir la clé
                if isinstance(k, (np.integer, np.floating)):
                    converted_key = int(k) if isinstance(k, np.integer) else float(k)
                else:
                    converted_key = k
                # Convertir la valeur
                converted_dict[converted_key] = self._convert_to_json_serializable(v)
            return converted_dict
        elif isinstance(obj, (list, tuple)):
            return [self._convert_to_json_serializable(item) for item in obj]
        elif isinstance(obj, set):
            return list(obj)
        else:
            return obj
    
    def to_dict(self) -> Dict[str, Any]:
        """Retourne l'analyse complète sous forme de dictionnaire sérialisable JSON."""
        data = {
            "input_count": len(self.input_grids),
            "raw_differences": self.raw_differences,
            "analysis": self.analysis,
            "notable": self.notable
        }
        return self._convert_to_json_serializable(data)


def analyze_input_differences(input_grids: List[np.ndarray]) -> Dict[str, Any]:
    """Fonction utilitaire pour analyser les différences entre inputs."""
    analyzer = InputDifferences(input_grids)
    return analyzer.to_dict()


# Exemple d'utilisation et test
if __name__ == "__main__":
    import argparse
    import json
    import sys
    import os
    
    # Forcer l'encodage UTF-8 pour éviter les problèmes d'affichage
    if sys.stdout.encoding != 'utf-8':
        sys.stdout.reconfigure(encoding='utf-8', errors='replace')
    
    def main():
        parser = argparse.ArgumentParser(description="Analyse des différences entre grilles input")
        parser.add_argument("--test", action="store_true", help="Exécuter un test avec des données d'exemple")
        parser.add_argument("--taskid", help="ID de la tâche ARC à analyser (ex: 007bbfb7). Si omis, analyse toutes les tâches")
        parser.add_argument("--subset", choices=["training", "evaluation"], default="training",
                           help="Subset de données (default: training)")
        args = parser.parse_args()
        
        if args.taskid:
            # Analyser une tâche ARC réelle
            from pathlib import Path
            
            task_file = Path(f"../../arcdata/{args.subset}/{args.taskid}.json")
            if not task_file.exists():
                print(f"❌ Fichier de tâche non trouvé: {task_file}")
                return
            
            with open(task_file, 'r') as f:
                task_data = json.load(f)
            
            train_examples = task_data.get('train', [])
            if len(train_examples) < 2:
                print(f"❌ Pas assez d'exemples train ({len(train_examples)}) pour analyser les différences")
                return
            
            # Extraire les grilles input
            input_grids = [np.array(ex['input']) for ex in train_examples]
            
            print(f"🔍 Analyse des différences entre inputs - Tâche {args.taskid}")
            print("=" * 60)
            
            analysis = analyze_input_differences(input_grids)
            
            # Afficher les résultats
            print(f"Nombre de grilles input analysées: {analysis['input_count']}")
            print(f"Subset: {args.subset}")
            
            print("\n📊 Insights clés:")
            for insight in analysis['notable']['key_insights']:
                print(f"  • {insight}")
            
            print("\n🔍 Patterns forts:")
            for pattern in analysis['notable']['strong_patterns']:
                print(f"  ✓ {pattern}")
            
            print("\n⚠️  Anomalies:")
            for anomaly in analysis['notable']['anomalies']:
                print(f"  ! {anomaly}")
            
            print("\n💡 Recommandations:")
            for rec in analysis['notable']['recommendations']:
                print(f"  → {rec}")
            
            print(f"\n📈 Score de confiance: {analysis['notable']['confidence_score']:.1%}")
            
            # Sauvegarder en JSON pour inspection
            import os
            results_dir = f"results/{args.subset}"
            os.makedirs(results_dir, exist_ok=True)
            output_file = f"{results_dir}/{args.taskid}_input_differences_test.json"
            with open(output_file, 'w') as f:
                json.dump(analysis, f, indent=2)
            print(f"\n💾 Résultats sauvegardés dans '{output_file}'")
        
        elif args.test:
            # Test avec des grilles d'exemple
            test_grids = [
                np.array([[0, 7, 7], [7, 7, 7], [0, 7, 7]]),
                np.array([[4, 0, 4], [0, 0, 0], [0, 4, 0]]),
                np.array([[0, 0, 0], [0, 0, 2], [2, 0, 2]])
            ]
            
            print("🔍 Test d'analyse des différences entre inputs")
            print("=" * 50)
            
            analysis = analyze_input_differences(test_grids)
            
            # Afficher les résultats
            print(f"Nombre de grilles analysées: {analysis['input_count']}")
            print("\n📊 Insights clés:")
            for insight in analysis['notable']['key_insights']:
                print(f"  • {insight}")
            
            print("\n🔍 Patterns forts:")
            for pattern in analysis['notable']['strong_patterns']:
                print(f"  ✓ {pattern}")
            
            print(f"\n📈 Score de confiance: {analysis['notable']['confidence_score']:.1%}")
            
            # Sauvegarder en JSON pour inspection
            import os
            os.makedirs("results", exist_ok=True)
            with open('results/test_input_differences.json', 'w') as f:
                json.dump(analysis, f, indent=2)
            print("\n💾 Résultats sauvegardés dans 'results/test_input_differences.json'")
        
        else:
            # Analyser toutes les tâches du subset
            from pathlib import Path
            import glob
            
            subset_dir = Path(f"../../arcdata/{args.subset}")
            if not subset_dir.exists():
                print(f"❌ Répertoire non trouvé: {subset_dir}")
                return
            
            task_files = [f for f in subset_dir.glob("*.json") if not f.name.endswith("_arc_analysis.json")]
            if not task_files:
                print(f"❌ Aucune tâche trouvée dans {subset_dir}")
                return
            
            print(f"[*] Analyse des différences entre inputs - Toutes les tâches du subset {args.subset}")
            print(f"[*] {len(task_files)} tâches trouvées")
            print("=" * 60)
            
            successful_analyses = 0
            failed_analyses = 0
            
            for task_file in sorted(task_files):
                task_id = task_file.stem
                print(f"\n[>] Analyse de la tâche {task_id}...")
                
                try:
                    with open(task_file, 'r') as f:
                        task_data = json.load(f)
                    
                    train_examples = task_data.get('train', [])
                    if len(train_examples) < 2:
                        print(f"  [!] Pas assez d'exemples train ({len(train_examples)})")
                        failed_analyses += 1
                        continue
                    
                    # Extraire les grilles input
                    input_grids = [np.array(example['input']) for example in train_examples]
                    
                    # Analyser
                    analysis = analyze_input_differences(input_grids)
                    
                    # Sauvegarder
                    import os
                    results_dir = f"results/{args.subset}"
                    os.makedirs(results_dir, exist_ok=True)
                    output_file = f"{results_dir}/{task_id}_input_differences.json"
                    with open(output_file, 'w') as f:
                        json.dump(analysis, f, indent=2)
                    
                    # Afficher résumé
                    confidence = analysis['notable']['confidence_score']
                    insights_count = len(analysis['notable']['key_insights'])
                    patterns_count = len(analysis['notable']['strong_patterns'])
                    
                    print(f"  [+] Terminé - Confiance: {confidence:.1%}, {insights_count} insights, {patterns_count} patterns")
                    successful_analyses += 1
                    
                except Exception as e:
                    print(f"  [-] Erreur: {e}")
                    failed_analyses += 1
            
            print(f"\n[*] Résumé final:")
            print(f"  [+] Analyses réussies: {successful_analyses}")
            print(f"  [-] Analyses échouées: {failed_analyses}")
            print(f"  [*] Résultats dans: results/{args.subset}/")
            
            if successful_analyses == 0:
                print("\n[!] Aucune analyse réussie. Utilisez --test pour un exemple ou --taskid pour une tâche spécifique.")
            print("  python input_differences.py --taskid 007bbfb7 --subset evaluation")
            print("  python input_differences.py --test")
    
    main()