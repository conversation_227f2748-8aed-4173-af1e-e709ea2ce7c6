# Synthèse Approfondie des Correspondances Caractéristiques-Scénarios ARC

## Métadonnées de l'Analyse

- **Tâches analysées**: 318
- **Corrélations fortes identifiées**: 26
- **Règles de transformation générées**: 19

## Patterns Dimensionnels

### Patterns d'Augmentation de Taille

- **(6, 6)**: 30 occurrences, commandes principales: MOTIF, PASTE
- **(3, 3)**: 31 occurrences, commandes principales: MOTIF, PASTE
- **(3, 6)**: 15 occurrences, commandes principales: MOTIF, PASTE
- **(5, 9)**: 39 occurrences, commandes principales: MOTIF, PASTE

### Patterns de Diminution de Taille

- **(-5, -5)**: 42 occurrences, commandes principales: MOTIF, CUT, PASTE

### Patterns de Tailles Spécifiques

- **(9, 9)**: 107 occurrences, commandes principales: MOTIF, PASTE
- **(13, 13)**: 40 occurrences, commandes principales: MOTIF, PASTE
- **(30, 30)**: 39 occurrences, commandes principales: MOTIF, PASTE
- **(30, 30)**: 39 occurrences, commandes principales: MOTIF, PASTE
- **(1, 5)**: 39 occurrences, commandes principales: MOTIF, PASTE
- **(11, 11)**: 35 occurrences, commandes principales: MOTIF, PASTE
- **(14, 14)**: 35 occurrences, commandes principales: MOTIF, PASTE
- **(21, 21)**: 25 occurrences, commandes principales: MOTIF, PASTE
- **(21, 21)**: 24 occurrences, commandes principales: MOTIF, PASTE
- **(10, 7)**: 24 occurrences, commandes principales: MOTIF, CUT, PASTE

## Analyse des Commandes

### Hiérarchie des Commandes

- **Core**: MOTIF, PASTE
- **Common**: EDIT
- **Specialized**: CUT, FILL, COLOR
- **Rare**: RESIZE, COPY, MULTIPLY, TRANSFERT, FLIP

### Paires de Commandes Fréquentes

- **MOTIF + PASTE**: 1534 co-occurrences
- **PASTE + MOTIF**: 1534 co-occurrences
- **MOTIF + CUT**: 152 co-occurrences
- **PASTE + CUT**: 152 co-occurrences
- **CUT + MOTIF**: 152 co-occurrences

## Règles de Transformation

### Size Increase

- **Condition**: dimension_change == (5, 9)
  - **Commandes recommandées**: MOTIF, PASTE
  - **Confiance**: 39 occurrences
  - **Description**: Quand les dimensions augmentent de (5, 9), utiliser principalement MOTIF, PASTE

- **Condition**: dimension_change == (3, 3)
  - **Commandes recommandées**: MOTIF, PASTE
  - **Confiance**: 31 occurrences
  - **Description**: Quand les dimensions augmentent de (3, 3), utiliser principalement MOTIF, PASTE

- **Condition**: dimension_change == (6, 6)
  - **Commandes recommandées**: MOTIF, PASTE
  - **Confiance**: 30 occurrences
  - **Description**: Quand les dimensions augmentent de (6, 6), utiliser principalement MOTIF, PASTE

- **Condition**: dimension_change == (3, 6)
  - **Commandes recommandées**: MOTIF, PASTE
  - **Confiance**: 15 occurrences
  - **Description**: Quand les dimensions augmentent de (3, 6), utiliser principalement MOTIF, PASTE

### Size Decrease

- **Condition**: dimension_change == (-5, -5)
  - **Commandes recommandées**: MOTIF, CUT, PASTE
  - **Confiance**: 42 occurrences
  - **Description**: Quand les dimensions diminuent de (-5, -5), utiliser principalement MOTIF, CUT, PASTE

### Specific Size

- **Condition**: shape == (9, 9)
  - **Commandes recommandées**: MOTIF, PASTE
  - **Confiance**: 107 occurrences
  - **Description**: Pour les grilles de taille (9, 9), utiliser principalement MOTIF, PASTE

- **Condition**: shape == (13, 13)
  - **Commandes recommandées**: MOTIF, PASTE
  - **Confiance**: 40 occurrences
  - **Description**: Pour les grilles de taille (13, 13), utiliser principalement MOTIF, PASTE

- **Condition**: shape == (30, 30)
  - **Commandes recommandées**: MOTIF, PASTE
  - **Confiance**: 39 occurrences
  - **Description**: Pour les grilles de taille (30, 30), utiliser principalement MOTIF, PASTE

- **Condition**: shape == (30, 30)
  - **Commandes recommandées**: MOTIF, PASTE
  - **Confiance**: 39 occurrences
  - **Description**: Pour les grilles de taille (30, 30), utiliser principalement MOTIF, PASTE

- **Condition**: shape == (1, 5)
  - **Commandes recommandées**: MOTIF, PASTE
  - **Confiance**: 39 occurrences
  - **Description**: Pour les grilles de taille (1, 5), utiliser principalement MOTIF, PASTE

## Insights Stratégiques

### Stratégies Dominantes

- **Motif Based Approach**: 95.6%
  - La majorité des solutions utilisent une approche basée sur les motifs (MOTIF)
- **Direct Editing**: 47.2%
  - L'édition directe est utilisée dans environ la moitié des cas
- **Cutting Operations**: 38.7%
  - Les opérations de découpe sont utilisées dans environ un tiers des cas

### Indicateurs de Complexité

- **Simple Transformations**: ✓
  - Peu de redimensionnements explicites (8.2%) suggère des transformations principalement in-place
- **Pattern Multiplication**: ✓
  - Peu de multiplications (1.9%) suggère des transformations principalement unitaires

## Recommandations

### Pour l'Entraînement de Modèles

- Prioriser l'apprentissage des commandes MOTIF et PASTE (utilisées dans >95% des cas)
- Développer une compréhension fine des transformations dimensionnelles
- Intégrer la détection de patterns de taille spécifique (9x9, 21x21, etc.)

### Pour la Résolution de Puzzles

- Commencer par analyser les changements dimensionnels
- Identifier si la transformation préserve ou modifie la taille
- Utiliser MOTIF+PASTE comme stratégie de base
- Recourir à CUT pour les réductions de taille
- Utiliser EDIT pour les ajustements fins
