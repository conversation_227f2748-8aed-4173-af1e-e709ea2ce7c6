#!/usr/bin/env python3
"""
Script pour nettoyer tous les scénarios et lancer l'analyse complète.
"""

import os
import sys
from pathlib import Path
import logging

# Ajouter le chemin pour importer scenario_cleaner
sys.path.append('src/grid_analysis/scenarios')
from scenario_cleaner import clean_agi_scenario

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def clean_all_scenarios():
    """Nettoie tous les scénarios du répertoire arcdata/training."""
    
    input_dir = Path("arcdata/training")
    output_dir = Path("src/grid_analysis/scenarios/training/reduced")
    
    # Créer le répertoire de sortie
    output_dir.mkdir(parents=True, exist_ok=True)
    
    # Trouver tous les fichiers VALID.agi
    scenario_files = list(input_dir.glob("*_TEST0_VALID.agi"))
    
    if not scenario_files:
        logger.error(f"Aucun fichier de scénario trouvé dans {input_dir}")
        return False
    
    logger.info(f"Trouvé {len(scenario_files)} scénarios à nettoyer")
    
    successful_cleanings = 0
    failed_cleanings = 0
    
    for scenario_file in sorted(scenario_files):
        try:
            # Lire le fichier
            with open(scenario_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # Extraire task_id du nom de fichier
            task_id = scenario_file.stem.replace('_TEST0_VALID', '')
            
            # Nettoyer le contenu
            cleaned_content, errors = clean_agi_scenario(content, task_id)
            
            if errors:
                logger.warning(f"Erreurs lors du nettoyage de {task_id}: {errors}")
                failed_cleanings += 1
                continue
            
            # Créer le nom de fichier de sortie
            output_filename = f"{task_id}_TEST0_REDUCED.agi"
            output_file = output_dir / output_filename
            
            # Sauvegarder
            with open(output_file, 'w', encoding='utf-8') as f:
                f.write(cleaned_content)
            
            successful_cleanings += 1
            
            if successful_cleanings % 50 == 0:
                logger.info(f"Nettoyé {successful_cleanings} scénarios...")
                
        except Exception as e:
            logger.error(f"Erreur lors du traitement de {scenario_file}: {e}")
            failed_cleanings += 1
    
    logger.info(f"Nettoyage terminé: {successful_cleanings} réussis, {failed_cleanings} échoués")
    
    return successful_cleanings > 0

def run_complete_analysis():
    """Lance l'analyse complète avec les scénarios nettoyés."""
    
    logger.info("Lancement de l'analyse complète...")
    
    # Scripts à exécuter dans l'ordre
    scripts = [
        "src/grid_analysis/enhanced_command_analyzer.py",
        "src/grid_analysis/enhanced_correlation_analyzer.py", 
        "src/grid_analysis/final_comprehensive_summary.py"
    ]
    
    for script in scripts:
        logger.info(f"Exécution de {script}...")
        
        try:
            result = os.system(f"python {script}")
            if result != 0:
                logger.error(f"Erreur lors de l'exécution de {script}")
                return False
        except Exception as e:
            logger.error(f"Exception lors de l'exécution de {script}: {e}")
            return False
    
    logger.info("Analyse complète terminée avec succès!")
    return True

def main():
    """Fonction principale."""
    
    print("🧹 Nettoyage et Analyse Complète des Scénarios ARC")
    print("=" * 60)
    
    # Étape 1: Nettoyer tous les scénarios
    print("\n📋 Étape 1: Nettoyage des scénarios...")
    if not clean_all_scenarios():
        print("❌ Échec du nettoyage des scénarios")
        return
    
    print("✅ Nettoyage des scénarios terminé")
    
    # Étape 2: Lancer l'analyse complète
    print("\n📊 Étape 2: Analyse complète...")
    if not run_complete_analysis():
        print("❌ Échec de l'analyse complète")
        return
    
    print("✅ Analyse complète terminée")
    
    # Résumé final
    print("\n🎉 Processus Complet Terminé!")
    print("=" * 40)
    print("📁 Résultats disponibles dans:")
    print("   • src/grid_analysis/enhanced_analysis/")
    print("   • src/grid_analysis/enhanced_correlation_analysis/")
    print("   • src/grid_analysis/final_comprehensive_analysis/")
    print("\n📖 Consultez FINAL_ANALYSIS_REPORT.md pour le résumé complet")

if __name__ == "__main__":
    main()