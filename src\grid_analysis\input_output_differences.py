"""
Analyse des différences entre INPUT et OUTPUT pour chaque exemple train.
Hiérarchie : <PERSON><PERSON>t → Analyse → Notable
"""

import numpy as np
from typing import Dict, List, Any, Tuple
from collections import Counter
try:
    from .grid_parameters import GridParameters
except ImportError:
    # Import direct si exécuté comme script
    from grid_parameters import GridParameters


class InputOutputDifferences:
    """
    Analyse des différences entre INPUT et OUTPUT pour chaque exemple train.
    
    Niveau 1 : Différences brutes (calculs directs)
    Niveau 2 : Analyses (patterns de transformation)  
    Niveau 3 : Notable (règles de transformation, insights)
    """
    
    def __init__(self, input_output_pairs: List[Tuple[np.ndarray, np.ndarray]]):
        """Initialise avec les paires (input, output) des exemples train."""
        self.pairs = input_output_pairs
        self.input_params = [GridParameters(pair[0]) for pair in input_output_pairs]
        self.output_params = [GridParameters(pair[1]) for pair in input_output_pairs]
        
        # Calculs des 3 niveaux
        self.raw_differences = self._compute_raw_differences()
        self.analysis = self._compute_analysis()
        self.notable = self._compute_notable()
    
    def _compute_raw_differences(self) -> Dict[str, Any]:
        """Niveau 1 : Différences brutes entre input et output pour chaque paire."""
        if len(self.pairs) == 0:
            return {"error": "Aucune paire input/output à analyser"}
        
        raw = {
            "pair_count": len(self.pairs),
            "transformation_data": self._get_transformation_data(),
            "dimension_transformations": self._get_dimension_transformations(),
            "color_transformations": self._get_color_transformations(),
            "density_transformations": self._get_density_transformations(),
            "structural_transformations": self._get_structural_transformations(),
            "pixel_level_changes": self._get_pixel_level_changes()
        }
        
        return raw
    
    def _get_transformation_data(self) -> List[Dict[str, Any]]:
        """Données de transformation pour chaque paire."""
        transformations = []
        
        for i, (input_grid, output_grid) in enumerate(self.pairs):
            input_params = self.input_params[i]
            output_params = self.output_params[i]
            
            transform = {
                "pair_index": i,
                "input_shape": input_grid.shape,
                "output_shape": output_grid.shape,
                "dimension_change": (output_grid.shape[1] - input_grid.shape[1], 
                                   output_grid.shape[0] - input_grid.shape[0]),
                "size_change": output_params.total_cells - input_params.total_cells,
                "size_ratio": output_params.total_cells / input_params.total_cells if input_params.total_cells > 0 else float('inf'),
                "color_count_change": output_params.unique_colors_count - input_params.unique_colors_count,
                "density_change": output_params.colored_density - input_params.colored_density,
                "dominant_color_preserved": input_params.dominant_color == output_params.dominant_color,
                "transformation_type": self._classify_transformation_type(input_grid, output_grid)
            }
            
            transformations.append(transform)
        
        return transformations
    
    def _classify_transformation_type(self, input_grid: np.ndarray, output_grid: np.ndarray) -> str:
        """Classifie le type de transformation basique."""
        input_shape = input_grid.shape
        output_shape = output_grid.shape
        
        if input_shape == output_shape:
            if np.array_equal(input_grid, output_grid):
                return "identity"
            else:
                return "in_place_modification"
        elif output_shape[0] > input_shape[0] or output_shape[1] > input_shape[1]:
            # Vérifier si c'est une répétition/tiling
            if (output_shape[0] % input_shape[0] == 0 and 
                output_shape[1] % input_shape[1] == 0):
                return "tiling_expansion"
            else:
                return "size_expansion"
        elif output_shape[0] < input_shape[0] or output_shape[1] < input_shape[1]:
            return "size_reduction"
        else:
            return "unknown"
    
    def _get_dimension_transformations(self) -> Dict[str, Any]:
        """Analyse des transformations dimensionnelles."""
        transform_data = self._get_transformation_data()
        
        dimension_changes = [t["dimension_change"] for t in transform_data]
        size_ratios = [t["size_ratio"] for t in transform_data if not np.isinf(t["size_ratio"])]
        
        return {
            "dimension_changes": dimension_changes,
            "unique_dimension_changes": list(set(dimension_changes)),
            "consistent_dimension_change": len(set(dimension_changes)) == 1,
            "size_ratios": size_ratios,
            "consistent_size_ratio": len(set(round(r, 2) for r in size_ratios)) == 1 if size_ratios else False,
            "common_size_ratio": size_ratios[0] if size_ratios and len(set(round(r, 2) for r in size_ratios)) == 1 else None,
            "transformation_types": [t["transformation_type"] for t in transform_data],
            "dominant_transformation_type": Counter([t["transformation_type"] for t in transform_data]).most_common(1)[0][0] if transform_data else None
        }
    
    def _get_color_transformations(self) -> Dict[str, Any]:
        """Analyse des transformations chromatiques."""
        color_changes = []
        palette_changes = []
        dominant_preserved = []
        
        for i in range(len(self.pairs)):
            input_params = self.input_params[i]
            output_params = self.output_params[i]
            
            color_change = output_params.unique_colors_count - input_params.unique_colors_count
            color_changes.append(color_change)
            
            input_palette = set(input_params.color_palette)
            output_palette = set(output_params.color_palette)
            
            palette_change = {
                "added_colors": list(output_palette - input_palette),
                "removed_colors": list(input_palette - output_palette),
                "preserved_colors": list(input_palette & output_palette),
                "palette_expansion": len(output_palette) > len(input_palette),
                "palette_reduction": len(output_palette) < len(input_palette),
                "palette_substitution": len(output_palette) == len(input_palette) and output_palette != input_palette
            }
            palette_changes.append(palette_change)
            
            dominant_preserved.append(input_params.dominant_color == output_params.dominant_color)
        
        return {
            "color_count_changes": color_changes,
            "consistent_color_change": len(set(color_changes)) == 1,
            "common_color_change": color_changes[0] if len(set(color_changes)) == 1 else None,
            "palette_changes": palette_changes,
            "dominant_color_preservation_rate": sum(dominant_preserved) / len(dominant_preserved) if dominant_preserved else 0.0,
            "color_transformation_patterns": self._analyze_color_transformation_patterns(palette_changes)
        }
    
    def _analyze_color_transformation_patterns(self, palette_changes: List[Dict]) -> Dict[str, Any]:
        """Analyse les patterns de transformation chromatique."""
        patterns = {
            "always_adds_colors": all(pc["palette_expansion"] for pc in palette_changes),
            "always_removes_colors": all(pc["palette_reduction"] for pc in palette_changes),
            "always_substitutes_colors": all(pc["palette_substitution"] for pc in palette_changes),
            "consistent_additions": self._check_consistent_color_additions(palette_changes),
            "consistent_removals": self._check_consistent_color_removals(palette_changes)
        }
        
        return patterns
    
    def _check_consistent_color_additions(self, palette_changes: List[Dict]) -> Dict[str, Any]:
        """Vérifie si les ajouts de couleurs sont cohérents."""
        additions = [set(pc["added_colors"]) for pc in palette_changes]
        
        if not additions or all(not add for add in additions):
            return {"consistent": True, "pattern": "no_additions", "common_additions": []}
        
        # Vérifier si les mêmes couleurs sont toujours ajoutées
        common_additions = set.intersection(*[add for add in additions if add])
        all_additions = set.union(*additions)
        
        return {
            "consistent": len(common_additions) == len(all_additions) and len(all_additions) > 0,
            "pattern": "same_colors_added" if len(common_additions) == len(all_additions) and len(all_additions) > 0 else "variable_additions",
            "common_additions": list(common_additions),
            "all_additions": list(all_additions)
        }
    
    def _check_consistent_color_removals(self, palette_changes: List[Dict]) -> Dict[str, Any]:
        """Vérifie si les suppressions de couleurs sont cohérentes."""
        removals = [set(pc["removed_colors"]) for pc in palette_changes]
        
        if not removals or all(not rem for rem in removals):
            return {"consistent": True, "pattern": "no_removals", "common_removals": []}
        
        common_removals = set.intersection(*[rem for rem in removals if rem])
        all_removals = set.union(*removals)
        
        return {
            "consistent": len(common_removals) == len(all_removals) and len(all_removals) > 0,
            "pattern": "same_colors_removed" if len(common_removals) == len(all_removals) and len(all_removals) > 0 else "variable_removals",
            "common_removals": list(common_removals),
            "all_removals": list(all_removals)
        }
    
    def _get_density_transformations(self) -> Dict[str, Any]:
        """Analyse des transformations de densité."""
        density_changes = []
        
        for i in range(len(self.pairs)):
            input_params = self.input_params[i]
            output_params = self.output_params[i]
            
            density_change = output_params.colored_density - input_params.colored_density
            density_changes.append(density_change)
        
        return {
            "density_changes": density_changes,
            "consistent_density_change": len(set(round(dc, 3) for dc in density_changes)) == 1,
            "average_density_change": sum(density_changes) / len(density_changes) if density_changes else 0.0,
            "density_trend": self._get_trend(density_changes),
            "always_increases_density": all(dc > 0.01 for dc in density_changes),
            "always_decreases_density": all(dc < -0.01 for dc in density_changes),
            "preserves_density": all(abs(dc) < 0.01 for dc in density_changes)
        }
    
    def _get_structural_transformations(self) -> Dict[str, Any]:
        """Analyse des transformations structurelles."""
        structural_changes = []
        
        for i in range(len(self.pairs)):
            input_params = self.input_params[i]
            output_params = self.output_params[i]
            
            # Changement de complexité (couleurs × taille)
            input_complexity = input_params.unique_colors_count * input_params.total_cells
            output_complexity = output_params.unique_colors_count * output_params.total_cells
            complexity_change = output_complexity - input_complexity
            
            # Changement de régularité (basé sur la distribution des couleurs)
            input_regularity = self._calculate_regularity(input_params)
            output_regularity = self._calculate_regularity(output_params)
            regularity_change = output_regularity - input_regularity
            
            structural_change = {
                "complexity_change": complexity_change,
                "regularity_change": regularity_change,
                "becomes_more_regular": regularity_change > 0.1,
                "becomes_more_complex": complexity_change > 0
            }
            
            structural_changes.append(structural_change)
        
        return {
            "structural_changes": structural_changes,
            "complexity_trend": self._get_trend([sc["complexity_change"] for sc in structural_changes]),
            "regularity_trend": self._get_trend([sc["regularity_change"] for sc in structural_changes]),
            "always_increases_complexity": all(sc["becomes_more_complex"] for sc in structural_changes),
            "always_increases_regularity": all(sc["becomes_more_regular"] for sc in structural_changes)
        }
    
    def _calculate_regularity(self, params: GridParameters) -> float:
        """Calcule un score de régularité basé sur la distribution des couleurs."""
        if params.unique_colors_count <= 1:
            return 1.0
        
        # Entropie normalisée
        total = params.total_cells
        entropy = 0.0
        for count in params.color_distribution.values():
            if count > 0:
                p = count / total
                entropy -= p * np.log2(p)
        
        max_entropy = np.log2(params.unique_colors_count)
        return 1.0 - (entropy / max_entropy if max_entropy > 0 else 0)
    
    def _get_pixel_level_changes(self) -> List[Dict[str, Any]]:
        """Analyse des changements au niveau pixel pour chaque paire."""
        pixel_changes = []
        
        for i, (input_grid, output_grid) in enumerate(self.pairs):
            if input_grid.shape == output_grid.shape:
                # Comparaison directe possible
                diff_matrix = (input_grid != output_grid).astype(int)
                total_changes = np.sum(diff_matrix)
                change_percentage = total_changes / input_grid.size * 100
                
                # Analyse des changements de couleur
                color_changes = {}
                for row in range(input_grid.shape[0]):
                    for col in range(input_grid.shape[1]):
                        if input_grid[row, col] != output_grid[row, col]:
                            change_key = f"{input_grid[row, col]}→{output_grid[row, col]}"
                            color_changes[change_key] = color_changes.get(change_key, 0) + 1
                
                pixel_change = {
                    "pair_index": i,
                    "comparable": True,
                    "total_changes": int(total_changes),
                    "change_percentage": float(change_percentage),
                    "color_changes": color_changes,
                    "unchanged_pixels": int(input_grid.size - total_changes),
                    "preservation_rate": float((input_grid.size - total_changes) / input_grid.size * 100)
                }
            else:
                # Grilles de tailles différentes
                pixel_change = {
                    "pair_index": i,
                    "comparable": False,
                    "reason": "different_dimensions",
                    "input_size": int(input_grid.size),
                    "output_size": int(output_grid.size),
                    "size_change": int(output_grid.size - input_grid.size)
                }
            
            pixel_changes.append(pixel_change)
        
        return pixel_changes
    
    def _get_trend(self, values: List[float]) -> str:
        """Détermine la tendance d'une série de valeurs."""
        if len(values) < 2:
            return "insufficient_data"
        
        if all(abs(v) < 0.01 for v in values):
            return "constant"
        elif all(v > 0.01 for v in values):
            return "increasing"
        elif all(v < -0.01 for v in values):
            return "decreasing"
        else:
            return "variable"
    
    def _compute_analysis(self) -> Dict[str, Any]:
        """Niveau 2 : Analyse des patterns de transformation."""
        raw = self.raw_differences
        
        if "error" in raw:
            return {"error": raw["error"]}
        
        analysis = {
            "transformation_consistency": self._analyze_transformation_consistency(),
            "scaling_patterns": self._analyze_scaling_patterns(),
            "color_transformation_rules": self._analyze_color_transformation_rules(),
            "structural_transformation_patterns": self._analyze_structural_transformation_patterns(),
            "preservation_patterns": self._analyze_preservation_patterns(),
            "transformation_complexity": self._analyze_transformation_complexity()
        }
        
        return analysis
    
    def _analyze_transformation_consistency(self) -> Dict[str, Any]:
        """Analyse la cohérence des transformations."""
        dim_trans = self.raw_differences["dimension_transformations"]
        color_trans = self.raw_differences["color_transformations"]
        
        consistency = {
            "dimension_consistency": dim_trans["consistent_dimension_change"],
            "size_ratio_consistency": dim_trans["consistent_size_ratio"],
            "color_change_consistency": color_trans["consistent_color_change"],
            "transformation_type_consistency": len(set(dim_trans["transformation_types"])) == 1,
            "overall_consistency_score": 0.0
        }
        
        # Score de cohérence global
        scores = [
            1.0 if consistency["dimension_consistency"] else 0.0,
            1.0 if consistency["size_ratio_consistency"] else 0.0,
            1.0 if consistency["color_change_consistency"] else 0.0,
            1.0 if consistency["transformation_type_consistency"] else 0.0
        ]
        
        consistency["overall_consistency_score"] = sum(scores) / len(scores)
        
        return consistency
    
    def _analyze_scaling_patterns(self) -> Dict[str, Any]:
        """Analyse les patterns de mise à l'échelle."""
        dim_trans = self.raw_differences["dimension_transformations"]
        
        patterns = {
            "has_scaling": dim_trans["common_size_ratio"] is not None and dim_trans["common_size_ratio"] != 1.0,
            "scaling_factor": dim_trans["common_size_ratio"],
            "scaling_type": self._classify_scaling_type(dim_trans),
            "uniform_scaling": dim_trans["consistent_size_ratio"],
            "tiling_pattern": self._analyze_tiling_pattern()
        }
        
        return patterns
    
    def _classify_scaling_type(self, dim_trans: Dict[str, Any]) -> str:
        """Classifie le type de mise à l'échelle."""
        if not dim_trans["consistent_size_ratio"]:
            return "variable"
        
        ratio = dim_trans["common_size_ratio"]
        if ratio is None:
            return "none"
        elif ratio == 1.0:
            return "no_scaling"
        elif ratio > 1.0:
            if ratio == int(ratio):
                return "integer_expansion"
            else:
                return "fractional_expansion"
        else:
            return "reduction"
    
    def _analyze_tiling_pattern(self) -> Dict[str, Any]:
        """Analyse les patterns de tiling/répétition."""
        transform_data = self.raw_differences["transformation_data"]
        
        tiling_cases = [t for t in transform_data if t["transformation_type"] == "tiling_expansion"]
        
        if not tiling_cases:
            return {"detected": False}
        
        # Analyser les facteurs de tiling
        tiling_factors = []
        for case in tiling_cases:
            input_shape = case["input_shape"]
            output_shape = case["output_shape"]
            
            h_factor = output_shape[0] // input_shape[0]
            w_factor = output_shape[1] // input_shape[1]
            tiling_factors.append((h_factor, w_factor))
        
        return {
            "detected": True,
            "tiling_factors": tiling_factors,
            "consistent_tiling": len(set(tiling_factors)) == 1,
            "common_tiling_factor": tiling_factors[0] if len(set(tiling_factors)) == 1 else None
        }
    
    def _analyze_color_transformation_rules(self) -> Dict[str, Any]:
        """Analyse les règles de transformation chromatique."""
        color_trans = self.raw_differences["color_transformations"]
        patterns = color_trans["color_transformation_patterns"]
        
        rules = {
            "color_addition_rule": self._derive_color_addition_rule(patterns),
            "color_removal_rule": self._derive_color_removal_rule(patterns),
            "color_substitution_rule": self._derive_color_substitution_rule(),
            "dominant_color_rule": self._derive_dominant_color_rule(color_trans)
        }
        
        return rules
    
    def _derive_color_addition_rule(self, patterns: Dict[str, Any]) -> Dict[str, Any]:
        """Dérive la règle d'ajout de couleurs."""
        if patterns["always_adds_colors"]:
            consistent_adds = patterns["consistent_additions"]
            if consistent_adds["consistent"]:
                return {
                    "type": "always_add_specific_colors",
                    "colors": consistent_adds["common_additions"],
                    "confidence": 1.0
                }
            else:
                return {
                    "type": "always_add_variable_colors",
                    "possible_colors": consistent_adds["all_additions"],
                    "confidence": 0.7
                }
        else:
            return {"type": "no_consistent_addition", "confidence": 0.0}
    
    def _derive_color_removal_rule(self, patterns: Dict[str, Any]) -> Dict[str, Any]:
        """Dérive la règle de suppression de couleurs."""
        if patterns["always_removes_colors"]:
            consistent_removals = patterns["consistent_removals"]
            if consistent_removals["consistent"]:
                return {
                    "type": "always_remove_specific_colors",
                    "colors": consistent_removals["common_removals"],
                    "confidence": 1.0
                }
            else:
                return {
                    "type": "always_remove_variable_colors",
                    "possible_colors": consistent_removals["all_removals"],
                    "confidence": 0.7
                }
        else:
            return {"type": "no_consistent_removal", "confidence": 0.0}
    
    def _derive_color_substitution_rule(self) -> Dict[str, Any]:
        """Dérive la règle de substitution de couleurs."""
        pixel_changes = self.raw_differences["pixel_level_changes"]
        
        # Analyser les substitutions communes
        all_substitutions = {}
        for change in pixel_changes:
            if change.get("comparable", False):
                for sub, count in change["color_changes"].items():
                    all_substitutions[sub] = all_substitutions.get(sub, 0) + count
        
        if all_substitutions:
            most_common = max(all_substitutions.items(), key=lambda x: x[1])
            return {
                "type": "color_substitution_detected",
                "most_common_substitution": most_common[0],
                "frequency": most_common[1],
                "all_substitutions": all_substitutions,
                "confidence": 0.8 if most_common[1] > 1 else 0.5
            }
        else:
            return {"type": "no_substitution_pattern", "confidence": 0.0}
    
    def _derive_dominant_color_rule(self, color_trans: Dict[str, Any]) -> Dict[str, Any]:
        """Dérive la règle concernant la couleur dominante."""
        preservation_rate = color_trans["dominant_color_preservation_rate"]
        
        if preservation_rate >= 0.8:
            return {
                "type": "dominant_color_preserved",
                "preservation_rate": preservation_rate,
                "confidence": 0.9
            }
        elif preservation_rate <= 0.2:
            return {
                "type": "dominant_color_always_changes",
                "preservation_rate": preservation_rate,
                "confidence": 0.9
            }
        else:
            return {
                "type": "dominant_color_variable",
                "preservation_rate": preservation_rate,
                "confidence": 0.5
            }
    
    def _analyze_structural_transformation_patterns(self) -> Dict[str, Any]:
        """Analyse les patterns de transformation structurelle."""
        struct_trans = self.raw_differences["structural_transformations"]
        
        patterns = {
            "complexity_evolution": struct_trans["complexity_trend"],
            "regularity_evolution": struct_trans["regularity_trend"],
            "consistent_complexity_increase": struct_trans["always_increases_complexity"],
            "consistent_regularity_increase": struct_trans["always_increases_regularity"],
            "structural_transformation_type": self._classify_structural_transformation(struct_trans)
        }
        
        return patterns
    
    def _classify_structural_transformation(self, struct_trans: Dict[str, Any]) -> str:
        """Classifie le type de transformation structurelle."""
        if struct_trans["always_increases_complexity"] and struct_trans["always_increases_regularity"]:
            return "complexification_with_regularization"
        elif struct_trans["always_increases_complexity"]:
            return "pure_complexification"
        elif struct_trans["always_increases_regularity"]:
            return "pure_regularization"
        elif struct_trans["complexity_trend"] == "constant" and struct_trans["regularity_trend"] == "constant":
            return "structural_preservation"
        else:
            return "mixed_structural_changes"
    
    def _analyze_preservation_patterns(self) -> Dict[str, Any]:
        """Analyse les patterns de préservation."""
        pixel_changes = self.raw_differences["pixel_level_changes"]
        
        comparable_changes = [pc for pc in pixel_changes if pc.get("comparable", False)]
        
        if not comparable_changes:
            return {"analysis_possible": False, "reason": "no_comparable_grids"}
        
        preservation_rates = [pc["preservation_rate"] for pc in comparable_changes]
        
        patterns = {
            "analysis_possible": True,
            "average_preservation_rate": sum(preservation_rates) / len(preservation_rates),
            "min_preservation_rate": min(preservation_rates),
            "max_preservation_rate": max(preservation_rates),
            "high_preservation": all(pr > 70 for pr in preservation_rates),
            "low_preservation": all(pr < 30 for pr in preservation_rates),
            "consistent_preservation": len(set(round(pr, 1) for pr in preservation_rates)) == 1,
            "preservation_trend": self._get_trend([pr - 50 for pr in preservation_rates])  # Centré sur 50%
        }
        
        return patterns
    
    def _analyze_transformation_complexity(self) -> Dict[str, Any]:
        """Analyse la complexité des transformations."""
        transform_data = self.raw_differences["transformation_data"]
        
        complexity_factors = []
        for transform in transform_data:
            complexity = 0
            
            # Facteur de changement de taille
            if transform["size_ratio"] != 1.0:
                complexity += 1
            
            # Facteur de changement de couleurs
            if transform["color_count_change"] != 0:
                complexity += 1
            
            # Facteur de changement de densité
            if abs(transform["density_change"]) > 0.1:
                complexity += 1
            
            # Facteur de type de transformation
            if transform["transformation_type"] not in ["identity", "in_place_modification"]:
                complexity += 1
            
            complexity_factors.append(complexity)
        
        return {
            "complexity_scores": complexity_factors,
            "average_complexity": sum(complexity_factors) / len(complexity_factors) if complexity_factors else 0,
            "max_complexity": max(complexity_factors) if complexity_factors else 0,
            "min_complexity": min(complexity_factors) if complexity_factors else 0,
            "uniform_complexity": len(set(complexity_factors)) == 1,
            "complexity_classification": self._classify_transformation_complexity(complexity_factors)
        }
    
    def _classify_transformation_complexity(self, complexity_scores: List[int]) -> str:
        """Classifie la complexité des transformations."""
        if not complexity_scores:
            return "unknown"
        
        avg_complexity = sum(complexity_scores) / len(complexity_scores)
        
        if avg_complexity <= 1:
            return "simple"
        elif avg_complexity <= 2:
            return "moderate"
        elif avg_complexity <= 3:
            return "complex"
        else:
            return "very_complex"
    
    def _compute_notable(self) -> Dict[str, Any]:
        """Niveau 3 : Ce qui est notable dans les transformations input→output."""
        if "error" in self.raw_differences:
            return {"error": self.raw_differences["error"]}
        
        notable = {
            "transformation_rules": [],
            "key_insights": [],
            "anomalies": [],
            "strong_patterns": [],
            "predictive_rules": [],
            "recommendations": [],
            "confidence_score": 0.0
        }
        
        notable["transformation_rules"] = self._derive_transformation_rules()
        notable["key_insights"] = self._identify_key_insights()
        notable["anomalies"] = self._identify_anomalies()
        notable["strong_patterns"] = self._identify_strong_patterns()
        notable["predictive_rules"] = self._derive_predictive_rules()
        notable["recommendations"] = self._generate_recommendations()
        notable["confidence_score"] = self._calculate_confidence_score()
        
        return notable
    
    def _derive_transformation_rules(self) -> List[Dict[str, Any]]:
        """Dérive les règles de transformation principales."""
        rules = []
        
        analysis = self.analysis
        
        # Règle dimensionnelle
        if analysis["transformation_consistency"]["dimension_consistency"]:
            dim_trans = self.raw_differences["dimension_transformations"]
            if dim_trans["common_size_ratio"]:
                rules.append({
                    "type": "dimensional_rule",
                    "rule": f"Multiply grid size by {dim_trans['common_size_ratio']:.2f}",
                    "confidence": 0.9,
                    "applies_to": "all_examples"
                })
        
        # Règle de tiling
        tiling = analysis["scaling_patterns"]["tiling_pattern"]
        if tiling["detected"] and tiling["consistent_tiling"]:
            factor = tiling["common_tiling_factor"]
            rules.append({
                "type": "tiling_rule",
                "rule": f"Tile input grid {factor[0]}×{factor[1]} times",
                "confidence": 0.95,
                "applies_to": "all_examples"
            })
        
        # Règles chromatiques
        color_rules = analysis["color_transformation_rules"]
        if color_rules["color_addition_rule"]["confidence"] > 0.7:
            add_rule = color_rules["color_addition_rule"]
            rules.append({
                "type": "color_addition_rule",
                "rule": f"Always add colors: {add_rule.get('colors', [])}",
                "confidence": add_rule["confidence"],
                "applies_to": "all_examples"
            })
        
        return rules
    
    def _identify_key_insights(self) -> List[str]:
        """Identifie les insights clés sur les transformations."""
        insights = []
        
        analysis = self.analysis
        raw = self.raw_differences
        
        # Insights sur la cohérence
        consistency = analysis["transformation_consistency"]["overall_consistency_score"]
        if consistency > 0.8:
            insights.append("Transformations très cohérentes - règles uniformes applicables")
        elif consistency < 0.3:
            insights.append("Transformations très variables - chaque cas nécessite une analyse individuelle")
        
        # Insights sur le type de transformation
        dom_type = raw["dimension_transformations"]["dominant_transformation_type"]
        if dom_type == "tiling_expansion":
            insights.append("Transformation principale : expansion par tiling/répétition")
        elif dom_type == "in_place_modification":
            insights.append("Transformation principale : modification sur place (même taille)")
        elif dom_type == "size_expansion":
            insights.append("Transformation principale : expansion de taille")
        
        # Insights sur la préservation
        preservation = analysis["preservation_patterns"]
        if preservation.get("analysis_possible", False):
            avg_pres = preservation["average_preservation_rate"]
            if avg_pres > 80:
                insights.append(f"Forte préservation du contenu original ({avg_pres:.1f}% en moyenne)")
            elif avg_pres < 20:
                insights.append(f"Transformation radicale du contenu ({avg_pres:.1f}% préservé)")
        
        # Insights sur la complexité
        complexity = analysis["transformation_complexity"]["complexity_classification"]
        insights.append(f"Complexité des transformations : {complexity}")
        
        return insights
    
    def _identify_anomalies(self) -> List[str]:
        """Identifie les anomalies dans les transformations."""
        anomalies = []
        
        raw = self.raw_differences
        
        # Anomalies de cohérence
        if not raw["dimension_transformations"]["consistent_dimension_change"]:
            unique_changes = len(raw["dimension_transformations"]["unique_dimension_changes"])
            if unique_changes == len(self.pairs):
                anomalies.append("Chaque transformation a des changements dimensionnels uniques")
        
        # Anomalies de ratio de taille
        size_ratios = raw["dimension_transformations"]["size_ratios"]
        if size_ratios and (max(size_ratios) > 10 * min(size_ratios)):
            anomalies.append("Écart extrême dans les ratios de taille entre transformations")
        
        # Anomalies de préservation
        preservation = self.analysis["preservation_patterns"]
        if preservation.get("analysis_possible", False):
            rates = [pc["preservation_rate"] for pc in raw["pixel_level_changes"] if pc.get("comparable", False)]
            if rates and (max(rates) - min(rates) > 70):
                anomalies.append("Variation extrême des taux de préservation entre exemples")
        
        return anomalies
    
    def _identify_strong_patterns(self) -> List[str]:
        """Identifie les patterns forts dans les transformations."""
        patterns = []
        
        analysis = self.analysis
        
        # Patterns de cohérence forte
        if analysis["transformation_consistency"]["overall_consistency_score"] > 0.9:
            patterns.append("Cohérence parfaite des transformations")
        
        # Patterns de tiling
        tiling = analysis["scaling_patterns"]["tiling_pattern"]
        if tiling["detected"] and tiling["consistent_tiling"]:
            patterns.append(f"Pattern de tiling cohérent : {tiling['common_tiling_factor']}")
        
        # Patterns chromatiques forts
        color_rules = analysis["color_transformation_rules"]
        if color_rules["dominant_color_rule"]["confidence"] > 0.8:
            dom_rule = color_rules["dominant_color_rule"]
            patterns.append(f"Règle forte sur la couleur dominante : {dom_rule['type']}")
        
        # Patterns structurels
        struct_type = analysis["structural_transformation_patterns"]["structural_transformation_type"]
        if struct_type != "mixed_structural_changes":
            patterns.append(f"Pattern structurel cohérent : {struct_type}")
        
        return patterns
    
    def _derive_predictive_rules(self) -> List[Dict[str, Any]]:
        """Dérive des règles prédictives pour de nouveaux inputs."""
        rules = []
        
        analysis = self.analysis
        
        # Règle prédictive dimensionnelle
        if analysis["transformation_consistency"]["size_ratio_consistency"]:
            ratio = self.raw_differences["dimension_transformations"]["common_size_ratio"]
            rules.append({
                "type": "size_prediction",
                "rule": f"output_size = input_size × {ratio:.2f}",
                "confidence": 0.9,
                "parameters": {"ratio": ratio}
            })
        
        # Règle prédictive de tiling
        tiling = analysis["scaling_patterns"]["tiling_pattern"]
        if tiling["detected"] and tiling["consistent_tiling"]:
            factor = tiling["common_tiling_factor"]
            rules.append({
                "type": "tiling_prediction",
                "rule": f"Repeat input {factor[0]}×{factor[1]} times",
                "confidence": 0.95,
                "parameters": {"h_factor": factor[0], "w_factor": factor[1]}
            })
        
        # Règle prédictive chromatique
        color_rules = analysis["color_transformation_rules"]
        add_rule = color_rules["color_addition_rule"]
        if add_rule["confidence"] > 0.7:
            rules.append({
                "type": "color_addition_prediction",
                "rule": f"Add colors {add_rule.get('colors', [])} to palette",
                "confidence": add_rule["confidence"],
                "parameters": {"colors_to_add": add_rule.get("colors", [])}
            })
        
        return rules
    
    def _generate_recommendations(self) -> List[str]:
        """Génère des recommandations d'analyse."""
        recommendations = []
        
        analysis = self.analysis
        
        # Recommandations basées sur la cohérence
        consistency = analysis["transformation_consistency"]["overall_consistency_score"]
        if consistency > 0.8:
            recommendations.append("Cohérence élevée - implémenter les règles dérivées directement")
        elif consistency > 0.5:
            recommendations.append("Cohérence modérée - vérifier les exceptions avant d'appliquer les règles")
        else:
            recommendations.append("Faible cohérence - analyser chaque transformation individuellement")
        
        # Recommandations basées sur la complexité
        complexity = analysis["transformation_complexity"]["complexity_classification"]
        if complexity == "simple":
            recommendations.append("Transformations simples - rechercher des règles géométriques de base")
        elif complexity == "complex":
            recommendations.append("Transformations complexes - décomposer en étapes plus simples")
        
        # Recommandations basées sur le type de transformation
        dom_type = self.raw_differences["dimension_transformations"]["dominant_transformation_type"]
        if dom_type == "tiling_expansion":
            recommendations.append("Focus sur les algorithmes de répétition/tiling")
        elif dom_type == "in_place_modification":
            recommendations.append("Focus sur les règles de modification pixel par pixel")
        
        return recommendations
    
    def _calculate_confidence_score(self) -> float:
        """Calcule un score de confiance pour l'analyse des transformations."""
        if len(self.pairs) == 0:
            return 0.0
        
        scores = []
        
        # Score basé sur la cohérence des transformations
        consistency_score = self.analysis["transformation_consistency"]["overall_consistency_score"]
        scores.append(consistency_score)
        
        # Score basé sur le nombre d'exemples
        sample_score = min(1.0, len(self.pairs) / 5.0)
        scores.append(sample_score)
        
        # Score basé sur la complexité (transformations simples = plus fiables)
        complexity = self.analysis["transformation_complexity"]["average_complexity"]
        complexity_score = max(0.0, 1.0 - complexity / 4.0)  # Normalisation sur 4 (max complexity)
        scores.append(complexity_score)
        
        # Score basé sur la possibilité d'analyse pixel
        pixel_changes = self.raw_differences["pixel_level_changes"]
        comparable_count = sum(1 for pc in pixel_changes if pc.get("comparable", False))
        pixel_score = comparable_count / len(pixel_changes) if pixel_changes else 0.0
        scores.append(pixel_score)
        
        return sum(scores) / len(scores)
    
    def _convert_to_json_serializable(self, obj):
        """Convertit les types NumPy en types Python natifs pour JSON."""
        if isinstance(obj, np.integer):
            return int(obj)
        elif isinstance(obj, np.floating):
            return float(obj)
        elif isinstance(obj, (np.bool_, bool)):
            return bool(obj)
        elif isinstance(obj, np.ndarray):
            return obj.tolist()
        elif isinstance(obj, dict):
            # Convertir les clés ET les valeurs
            converted_dict = {}
            for k, v in obj.items():
                # Convertir la clé
                if isinstance(k, (np.integer, np.floating)):
                    converted_key = int(k) if isinstance(k, np.integer) else float(k)
                else:
                    converted_key = k
                # Convertir la valeur
                converted_dict[converted_key] = self._convert_to_json_serializable(v)
            return converted_dict
        elif isinstance(obj, (list, tuple)):
            return [self._convert_to_json_serializable(item) for item in obj]
        elif isinstance(obj, set):
            return list(obj)
        else:
            return obj
    
    def to_dict(self) -> Dict[str, Any]:
        """Retourne l'analyse complète sous forme de dictionnaire sérialisable JSON."""
        data = {
            "pair_count": len(self.pairs),
            "raw_differences": self.raw_differences,
            "analysis": self.analysis,
            "notable": self.notable
        }
        return self._convert_to_json_serializable(data)


def analyze_input_output_differences(input_output_pairs: List[Tuple[np.ndarray, np.ndarray]]) -> Dict[str, Any]:
    """Fonction utilitaire pour analyser les différences input→output."""
    analyzer = InputOutputDifferences(input_output_pairs)
    return analyzer.to_dict()


# Exemple d'utilisation et test
if __name__ == "__main__":
    import argparse
    import json
    import sys
    import os
    
    # Forcer l'encodage UTF-8 pour éviter les problèmes d'affichage
    if sys.stdout.encoding != 'utf-8':
        sys.stdout.reconfigure(encoding='utf-8', errors='replace')
    
    def main():
        parser = argparse.ArgumentParser(description="Analyse des transformations input→output")
        parser.add_argument("--test", action="store_true", help="Exécuter un test avec des données d'exemple")
        parser.add_argument("--taskid", help="ID de la tâche ARC à analyser (ex: 007bbfb7). Si omis, analyse toutes les tâches")
        parser.add_argument("--subset", choices=["training", "evaluation"], default="training",
                           help="Subset de données (default: training)")
        args = parser.parse_args()
        
        if args.taskid:
            # Analyser une tâche ARC réelle
            from pathlib import Path
            
            task_file = Path(f"../../arcdata/{args.subset}/{args.taskid}.json")
            if not task_file.exists():
                print(f"❌ Fichier de tâche non trouvé: {task_file}")
                return
            
            with open(task_file, 'r') as f:
                task_data = json.load(f)
            
            train_examples = task_data.get('train', [])
            if len(train_examples) < 1:
                print(f"❌ Pas d'exemples train pour analyser les transformations")
                return
            
            # Extraire les paires input→output
            input_output_pairs = [(np.array(ex['input']), np.array(ex['output'])) for ex in train_examples]
            
            print(f"🔍 Analyse des transformations input→output - Tâche {args.taskid}")
            print("=" * 60)
            
            analysis = analyze_input_output_differences(input_output_pairs)
            
            # Afficher les résultats
            print(f"Nombre de paires analysées: {analysis['pair_count']}")
            print(f"Subset: {args.subset}")
            
            print("\n🔄 Règles de transformation:")
            for rule in analysis['notable']['transformation_rules']:
                print(f"  📋 {rule['type']}: {rule['rule']} (confiance: {rule['confidence']:.1%})")
            
            print("\n📊 Insights clés:")
            for insight in analysis['notable']['key_insights']:
                print(f"  • {insight}")
            
            print("\n🔍 Patterns forts:")
            for pattern in analysis['notable']['strong_patterns']:
                print(f"  ✓ {pattern}")
            
            print("\n🎯 Règles prédictives:")
            for rule in analysis['notable']['predictive_rules']:
                print(f"  🔮 {rule['type']}: {rule['rule']} (confiance: {rule['confidence']:.1%})")
            
            print("\n⚠️  Anomalies:")
            for anomaly in analysis['notable']['anomalies']:
                print(f"  ! {anomaly}")
            
            print("\n💡 Recommandations:")
            for rec in analysis['notable']['recommendations']:
                print(f"  → {rec}")
            
            print(f"\n📈 Score de confiance: {analysis['notable']['confidence_score']:.1%}")
            
            # Sauvegarder en JSON pour inspection
            import os
            results_dir = f"results/{args.subset}"
            os.makedirs(results_dir, exist_ok=True)
            output_file = f"{results_dir}/{args.taskid}_io_differences_test.json"
            with open(output_file, 'w') as f:
                json.dump(analysis, f, indent=2)
            print(f"\n💾 Résultats sauvegardés dans '{output_file}'")
        
        elif args.test:
            # Test avec des paires d'exemple
            test_pairs = [
                (np.array([[0, 7, 7], [7, 7, 7], [0, 7, 7]]), 
                 np.array([[0, 0, 0, 0, 7, 7, 0, 7, 7], [0, 0, 0, 7, 7, 7, 7, 7, 7], [0, 0, 0, 0, 7, 7, 0, 7, 7]])),
                (np.array([[4, 0, 4], [0, 0, 0], [0, 4, 0]]), 
                 np.array([[4, 0, 4, 0, 0, 0, 4, 0, 4], [0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 4, 0, 0, 0, 0, 0, 4, 0]]))
            ]
            
            print("🔍 Test d'analyse des transformations input→output")
            print("=" * 50)
            
            analysis = analyze_input_output_differences(test_pairs)
            
            # Afficher les résultats
            print(f"Nombre de paires analysées: {analysis['pair_count']}")
            
            print("\n🔄 Règles de transformation:")
            for rule in analysis['notable']['transformation_rules']:
                print(f"  📋 {rule['type']}: {rule['rule']} (confiance: {rule['confidence']:.1%})")
            
            print("\n📊 Insights clés:")
            for insight in analysis['notable']['key_insights']:
                print(f"  • {insight}")
            
            print(f"\n📈 Score de confiance: {analysis['notable']['confidence_score']:.1%}")
            
            # Sauvegarder en JSON pour inspection
            import os
            os.makedirs("results", exist_ok=True)
            with open('results/test_io_differences.json', 'w') as f:
                json.dump(analysis, f, indent=2)
            print("\n💾 Résultats sauvegardés dans 'results/test_io_differences.json'")
        
        else:
            # Analyser toutes les tâches du subset
            from pathlib import Path
            import glob
            
            subset_dir = Path(f"../../arcdata/{args.subset}")
            if not subset_dir.exists():
                print(f"❌ Répertoire non trouvé: {subset_dir}")
                return
            
            task_files = [f for f in subset_dir.glob("*.json") if not f.name.endswith("_arc_analysis.json")]
            if not task_files:
                print(f"❌ Aucune tâche trouvée dans {subset_dir}")
                return
            
            print(f"[*] Analyse des transformations input->output - Toutes les tâches du subset {args.subset}")
            print(f"[*] {len(task_files)} tâches trouvées")
            print("=" * 60)
            
            successful_analyses = 0
            failed_analyses = 0
            
            for task_file in sorted(task_files):
                task_id = task_file.stem
                print(f"\n[>] Analyse de la tâche {task_id}...")
                
                try:
                    with open(task_file, 'r') as f:
                        task_data = json.load(f)
                    
                    train_examples = task_data.get('train', [])
                    if len(train_examples) < 1:
                        print(f"  [!] Aucun exemple train")
                        failed_analyses += 1
                        continue
                    
                    # Créer les paires input/output
                    input_output_pairs = [(np.array(ex['input']), np.array(ex['output'])) for ex in train_examples]
                    
                    # Analyser
                    analysis = analyze_input_output_differences(input_output_pairs)
                    
                    # Sauvegarder
                    import os
                    results_dir = f"results/{args.subset}"
                    os.makedirs(results_dir, exist_ok=True)
                    output_file = f"{results_dir}/{task_id}_io_differences.json"
                    with open(output_file, 'w') as f:
                        json.dump(analysis, f, indent=2)
                    
                    # Afficher résumé
                    confidence = analysis['notable']['confidence_score']
                    rules_count = len(analysis['notable']['transformation_rules'])
                    insights_count = len(analysis['notable']['key_insights'])
                    
                    print(f"  [+] Terminé - Confiance: {confidence:.1%}, {rules_count} règles, {insights_count} insights")
                    successful_analyses += 1
                    
                except Exception as e:
                    print(f"  [-] Erreur: {e}")
                    failed_analyses += 1
            
            print(f"\n[*] Résumé final:")
            print(f"  [+] Analyses réussies: {successful_analyses}")
            print(f"  [-] Analyses échouées: {failed_analyses}")
            print(f"  [*] Résultats dans: results/{args.subset}/")
            
            if successful_analyses == 0:
                print("\n[!] Aucune analyse réussie. Utilisez --test pour un exemple ou --taskid pour une tâche spécifique.")
            print("  python input_output_differences.py --test")
    
    main()