#!/usr/bin/env python3
"""
Nettoyeur de scénarios AGI - Supprime les lignes INIT/TRANSFERT et END
Usage: python scenario_cleaner.py --subset training --taskid 007bbfb7
       python scenario_cleaner.py --subset training  # Pour toutes les tâches
"""

import argparse
import json
import sys
import os
import re
from pathlib import Path
from typing import List, Tuple, Optional


def clean_agi_scenario(content: str, task_id: str) -> Tuple[str, List[str]]:
    """
    Nettoie un scénario AGI selon les règles spécifiées :
    - Garde INIT/TRANSFERT comme information d'initialisation
    - Simplifie les commandes MOTIF selon les règles de classification
    - Simplifie les commandes groupées (EDITS -> EDITS, etc.)
    - Supprime les paramètres et coordonnées des commandes individuelles
    - Supprime la ligne END
    
    Args:
        content: Contenu du fichier AGI
        task_id: ID de la tâche pour les messages d'erreur
        
    Returns:
        Tuple (contenu_nettoyé, erreurs)
    """
    lines = content.strip().split('\n')
    errors = []
    
    if not lines:
        errors.append(f"Fichier vide")
        return "", errors
    
    # Vérifier la première ligne (phase d'initialisation)
    first_line = lines[0].strip()
    if not re.match(r'^(INIT|TRANSFERT)', first_line):
        errors.append(f"Première ligne ne commence pas par INIT ou TRANSFERT: '{first_line}'")
        return "", errors
    
    # Vérifier la dernière ligne
    last_line = lines[-1].strip()
    if last_line != "END":
        errors.append(f"Dernière ligne n'est pas 'END': '{last_line}'")
        return "", errors
    
    if len(lines) < 2:
        errors.append(f"Fichier trop court (moins de 2 lignes)")
        return "", errors
    
    cleaned_lines = []
    
    # Phase d'initialisation : garder INIT ou TRANSFERT
    init_line = extract_initialization_info(first_line)
    cleaned_lines.append(init_line)
    
    # Phase actions : traiter les lignes entre la première et la dernière
    action_lines = lines[1:-1]  # Exclure première et dernière ligne
    
    for line in action_lines:
        line = line.strip()
        if not line:
            continue
            
        cleaned_line = clean_action_line(line)
        if cleaned_line:
            cleaned_lines.append(cleaned_line)
    
    if len(cleaned_lines) <= 1:  # Seulement la ligne d'initialisation
        errors.append(f"Aucune commande d'action restante après nettoyage")
        return "", errors
    
    return '\n'.join(cleaned_lines), errors


def extract_initialization_info(line: str) -> str:
    """
    Extrait l'information d'initialisation (INIT ou TRANSFERT).
    
    Args:
        line: Ligne d'initialisation
        
    Returns:
        Information d'initialisation simplifiée
    """
    line = line.strip()
    
    if line.startswith('INIT'):
        return 'INIT'
    elif line.startswith('TRANSFERT'):
        return 'TRANSFERT'
    else:
        return line  # Fallback


def clean_action_line(line: str) -> str:
    """
    Nettoie une ligne d'action selon les règles spécifiées.
    
    Args:
        line: Ligne d'action à nettoyer
        
    Returns:
        Ligne nettoyée
    """
    line = line.strip()
    
    # Traitement des commandes MOTIF
    if line.startswith('MOTIF'):
        return clean_motif_command(line)
    
    # Traitement des commandes groupées (avec S à la fin)
    elif line.startswith('EDITS'):
        return 'EDITS'
    elif line.startswith('REPLACES'):
        return 'REPLACES'
    elif line.startswith('SURROUNDS'):
        return 'SURROUNDS'
    elif line.startswith('FLOODFILLS'):
        return 'FLOODFILLS'
    elif line.startswith('CLEARS'):
        return 'CLEARS'
    
    # Traitement des commandes individuelles
    elif line.startswith('FLOODFILL'):
        return 'FLOODFILL'
    elif line.startswith('SURROUND'):
        return 'SURROUND'
    elif line.startswith('CLEAR'):
        return clean_clear_command(line)
    elif line.startswith('EDIT'):
        return 'EDIT'
    elif line.startswith('REPLACE'):
        return 'REPLACE'
    elif line.startswith('FILL'):
        return 'FILL'
    elif line.startswith('RESIZE'):
        return 'RESIZE'
    elif line.startswith('EXTRACT'):
        return 'EXTRACT'
    elif line.startswith('TRANSFERT'):
        return 'TRANSFERT'
    
    # Autres commandes : garder tel quel mais sans paramètres
    else:
        # Extraire le nom de la commande (premier mot)
        command_name = line.split()[0] if line.split() else line
        return command_name


def clean_motif_command(line: str) -> str:
    """
    Nettoie une commande MOTIF selon les règles de classification.
    
    Règles :
    - MOTIF + CUT/COPY + PASTE sans COLOR ni transformations -> MOVE
    - MOTIF + CUT/COPY + PASTE avec COLOR sans transformations -> MOVE MOTIF
    - MOTIF + CUT/COPY + PASTE avec transformations -> [TRANSFORMATION] ou [TRANSFORMATION] MOTIF
    
    Args:
        line: Ligne MOTIF à analyser
        
    Returns:
        Commande classifiée
    """
    # Analyser le contenu de la commande MOTIF
    has_cut = 'CUT' in line
    has_copy = 'COPY' in line
    has_paste = 'PASTE' in line
    has_color = 'COLOR' in line
    
    # Détecter les transformations
    transformations = []
    
    if 'FLIP VERTICAL' in line:
        transformations.append('FLIP')
    elif 'FLIP HORIZONTAL' in line:
        transformations.append('FLIP')
    elif 'FLIP' in line:
        transformations.append('FLIP')
    
    if 'ROTATE RIGHT' in line:
        transformations.append('ROTATE')
    elif 'ROTATE LEFT' in line:
        transformations.append('ROTATE')
    elif 'ROTATE' in line:
        transformations.append('ROTATE')
    
    if 'DIVIDE' in line:
        transformations.append('DIVIDE')
    
    if 'MULTIPLY' in line:
        transformations.append('MULTIPLY')
    
    if 'SCALE' in line:
        transformations.append('SCALE')
    
    if 'MIRROR' in line:
        transformations.append('MIRROR')
    
    # Appliquer les règles de classification
    has_basic_pattern = (has_cut or has_copy) and has_paste
    
    if not has_basic_pattern:
        return 'MOTIF'  # Cas particulier
    
    # Pas de transformations
    if not transformations:
        if has_color:
            return 'MOVE MOTIF'
        else:
            return 'MOVE'
    
    # Avec transformations
    else:
        transformation_str = ' '.join(transformations)
        if has_color:
            return f'{transformation_str} MOTIF'
        else:
            return transformation_str


def clean_clear_command(line: str) -> str:
    """
    Nettoie une commande CLEAR en gardant INVERT si présent.
    
    Args:
        line: Ligne CLEAR à analyser
        
    Returns:
        Commande CLEAR nettoyée
    """
    if 'INVERT' in line:
        return 'CLEAR INVERT'
    else:
        return 'CLEAR'


def process_single_scenario(scenario_file: Path, output_dir: Path) -> Tuple[bool, List[str]]:
    """
    Traite un seul fichier de scénario.
    
    Args:
        scenario_file: Chemin vers le fichier de scénario
        output_dir: Répertoire de sortie
        
    Returns:
        Tuple (succès, erreurs)
    """
    errors = []
    
    try:
        # Lire le fichier
        with open(scenario_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Extraire task_id et test_number du nom de fichier
        filename = scenario_file.name
        if not filename.endswith('_VALID.agi'):
            errors.append(f"Nom de fichier invalide: {filename}")
            return False, errors
        
        # Parser le nom: {task_id}_TEST{number}_VALID.agi
        base_name = filename[:-10]  # Supprimer '_VALID.agi'
        if not re.match(r'^[a-f0-9]{8}_TEST\d+$', base_name):
            errors.append(f"Format de nom invalide: {base_name}")
            return False, errors
        
        task_id = base_name.split('_TEST')[0]
        
        # Nettoyer le contenu
        cleaned_content, clean_errors = clean_agi_scenario(content, task_id)
        if clean_errors:
            errors.extend(clean_errors)
            return False, errors
        
        # Créer le nom de fichier de sortie (VALID -> REDUCED)
        output_filename = filename.replace('_VALID.agi', '_REDUCED.agi')
        output_file = output_dir / output_filename
        
        # Sauvegarder
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write(cleaned_content)
        
        return True, []
        
    except Exception as e:
        errors.append(f"Erreur lors du traitement: {str(e)}")
        return False, errors


if __name__ == "__main__":
    import sys
    import os
    
    # Forcer l'encodage UTF-8 pour éviter les problèmes d'affichage
    if sys.stdout.encoding != 'utf-8':
        sys.stdout.reconfigure(encoding='utf-8', errors='replace')
    
    def main():
        parser = argparse.ArgumentParser(description="Nettoyeur de scénarios AGI")
        parser.add_argument("--test", action="store_true", help="Exécuter un test avec un scénario d'exemple")
        parser.add_argument("--taskid", help="ID de la tâche à traiter (ex: 007bbfb7). Si omis, traite toutes les tâches")
        parser.add_argument("--subset", choices=["training", "evaluation"], default="training",
                           help="Subset de données (default: training)")
        args = parser.parse_args()
        
        if args.taskid:
            # Traiter une tâche spécifique
            scenario_dir = Path(f"../Scenarios/{args.subset}")
            if not scenario_dir.exists():
                print(f"[-] Répertoire non trouvé: {scenario_dir}")
                return
            
            # Chercher le fichier de scénario
            scenario_files = list(scenario_dir.glob(f"{args.taskid}_TEST*_VALID.agi"))
            if not scenario_files:
                print(f"[-] Aucun scénario trouvé pour la tâche {args.taskid}")
                return
            
            # Créer le répertoire de sortie
            output_dir = Path(f"{args.subset}/reduced")
            os.makedirs(output_dir, exist_ok=True)
            
            print(f"[*] Nettoyage des scénarios - Tâche {args.taskid}")
            print(f"[*] {len(scenario_files)} fichier(s) trouvé(s)")
            print("=" * 60)
            
            for scenario_file in scenario_files:
                try:
                    print(f"\n[>] Traitement de {scenario_file.name}...")
                except (BrokenPipeError, OSError):
                    break
                
                success, errors = process_single_scenario(scenario_file, output_dir)
                
                if success:
                    output_name = scenario_file.name.replace('_VALID.agi', '_REDUCED.agi')
                    try:
                        print(f"  [+] Terminé - Fichier créé: {output_name}")
                    except (BrokenPipeError, OSError):
                        break
                else:
                    print(f"  [-] Erreurs:")
                    for error in errors:
                        print(f"      {error}")
        
        elif args.test:
            # Test avec un scénario d'exemple
            test_content = """TRANSFERT {INIT 3x3; EDIT 7 ([0,0] [0,2] [1,0] [1,2] [2,0] [2,1])}
RESIZE 9x9
MOTIF {CUT (COLOR 7 [0,0 2,2]); MULTIPLY 3 true; PASTE [0,0]}
END"""
            
            print("[*] Test de nettoyage de scénario AGI")
            print("=" * 50)
            print("Contenu original:")
            for i, line in enumerate(test_content.split('\n'), 1):
                print(f"  {i}: {line}")
            
            cleaned_content, errors = clean_agi_scenario(test_content, "test")
            
            if errors:
                print(f"\n[-] Erreurs:")
                for error in errors:
                    print(f"  {error}")
            else:
                print(f"\n[+] Contenu nettoyé:")
                for i, line in enumerate(cleaned_content.split('\n'), 1):
                    print(f"  {i}: {line}")
        
        else:
            # Traiter toutes les tâches du subset
            scenario_dir = Path(f"../Scenarios/{args.subset}")
            if not scenario_dir.exists():
                print(f"[-] Répertoire non trouvé: {scenario_dir}")
                return
            
            scenario_files = list(scenario_dir.glob("*_VALID.agi"))
            if not scenario_files:
                print(f"[-] Aucun scénario trouvé dans {scenario_dir}")
                return
            
            # Créer le répertoire de sortie
            output_dir = Path(f"{args.subset}/reduced")
            os.makedirs(output_dir, exist_ok=True)
            
            print(f"[*] Nettoyage de tous les scénarios du subset {args.subset}")
            print(f"[*] {len(scenario_files)} fichiers trouvés")
            print("=" * 60)
            
            successful_cleanings = 0
            failed_cleanings = 0
            
            for scenario_file in sorted(scenario_files):
                try:
                    print(f"\n[>] Traitement de {scenario_file.name}...")
                except (BrokenPipeError, OSError):
                    break
                
                success, errors = process_single_scenario(scenario_file, output_dir)
                
                if success:
                    output_name = scenario_file.name.replace('_VALID.agi', '_REDUCED.agi')
                    try:
                        print(f"  [+] Terminé - Fichier créé: {output_name}")
                    except (BrokenPipeError, OSError):
                        break
                    successful_cleanings += 1
                else:
                    print(f"  [-] Erreurs:")
                    for error in errors:
                        print(f"      {error}")
                    failed_cleanings += 1
            
            print(f"\n[*] Résumé final:")
            print(f"  [+] Nettoyages réussis: {successful_cleanings}")
            print(f"  [-] Nettoyages échoués: {failed_cleanings}")
            print(f"  [*] Fichiers dans: {args.subset}/reduced/")
            
            if successful_cleanings == 0:
                print("\n[!] Aucun nettoyage réussi. Utilisez --test pour un exemple ou --taskid pour une tâche spécifique.")
    
    main()