#!/usr/bin/env python3
"""
Générateur de Scénarios AGI Amélioré pour les Tâches d'Évaluation

Ce script utilise l'ensemble complet des caractéristiques extraites pour prédire
avec précision les commandes AGI et déterminer s'il faut démarrer avec une grille
vierge (INIT) ou la grille d'entrée (TRANSFERT).

Basé sur l'analyse complète des données d'entraînement.
"""

import json
import os
import sys
from pathlib import Path
from typing import Dict, List, Any, Tuple, Optional
from collections import Counter, defaultdict
import numpy as np
import re

class EnhancedEvaluationScenarioGenerator:
    """
    Générateur de scénarios AGI amélioré utilisant toutes les caractéristiques
    disponibles pour une prédiction précise des commandes et du type d'initialisation.
    """
    
    def __init__(self):
        self.base_dir = Path(__file__).parent
        self.training_results_dir = self.base_dir / "results" / "training"
        self.evaluation_results_dir = self.base_dir / "results" / "evaluation"
        self.scenarios_dir = self.base_dir / "scenarios" / "evaluation_enhanced"
        self.training_scenarios_dir = Path("arcdata/training")
        
        # C<PERSON>er le répertoire de sortie
        self.scenarios_dir.mkdir(parents=True, exist_ok=True)
        
        # Charger les modèles de prédiction basés sur l'entraînement
        self.prediction_models = self._build_prediction_models()
        
    def _build_prediction_models(self) -> Dict[str, Any]:
        """Construit les modèles de prédiction basés sur l'analyse des données d'entraînement."""
        print("🔧 Construction des modèles de prédiction...")
        
        models = {
            'init_vs_transfert': self._build_init_transfert_model(),
            'command_prediction': self._build_command_prediction_model(),
            'transformation_patterns': self._build_transformation_patterns(),
            'size_change_rules': self._build_size_change_rules(),
            'color_transformation_rules': self._build_color_transformation_rules()
        }
        
        return models
    
    def _build_init_transfert_model(self) -> Dict[str, Any]:
        """Construit un modèle pour prédire INIT vs TRANSFERT."""
        init_patterns = []
        transfert_patterns = []
        
        # Analyser tous les scénarios d'entraînement
        for agi_file in self.training_scenarios_dir.glob("*_TEST0_VALID.agi"):
            task_id = agi_file.stem.replace("_TEST0_VALID", "")
            
            try:
                with open(agi_file, 'r', encoding='utf-8') as f:
                    content = f.read().strip()
                
                # Charger les caractéristiques de la tâche
                characteristics = self._load_training_task_characteristics(task_id)
                if not characteristics:
                    continue
                
                # Déterminer le type d'initialisation
                if content.startswith('INIT'):
                    init_patterns.append(characteristics)
                elif content.startswith('TRANSFERT'):
                    transfert_patterns.append(characteristics)
                    
            except Exception as e:
                continue
        
        print(f"   📊 Analysé {len(init_patterns)} patterns INIT et {len(transfert_patterns)} patterns TRANSFERT")
        
        return {
            'init_patterns': init_patterns,
            'transfert_patterns': transfert_patterns,
            'init_indicators': self._extract_init_indicators(init_patterns),
            'transfert_indicators': self._extract_transfert_indicators(transfert_patterns)
        }
    
    def _extract_init_indicators(self, patterns: List[Dict]) -> Dict[str, float]:
        """Extrait les indicateurs favorisant INIT."""
        indicators = defaultdict(int)
        total = len(patterns)
        
        if total == 0:
            return {}
        
        for pattern in patterns:
            io_diff = pattern.get('io_differences', {})
            
            # Analyser les transformations
            raw_diff = io_diff.get('raw_differences', {})
            transformation_data = raw_diff.get('transformation_data', [])
            
            for transform in transformation_data:
                # Changement de taille significatif
                size_ratio = transform.get('size_ratio', 1.0)
                if size_ratio > 2.0:
                    indicators['large_size_increase'] += 1
                elif size_ratio < 0.5:
                    indicators['large_size_decrease'] += 1
                
                # Type de transformation
                transform_type = transform.get('transformation_type', '')
                if 'expansion' in transform_type:
                    indicators['expansion_transform'] += 1
                elif 'reduction' in transform_type:
                    indicators['reduction_transform'] += 1
                
                # Changement de densité
                density_change = abs(transform.get('density_change', 0))
                if density_change > 0.3:
                    indicators['high_density_change'] += 1
        
        # Normaliser par le total
        return {k: v / total for k, v in indicators.items()}
    
    def _extract_transfert_indicators(self, patterns: List[Dict]) -> Dict[str, float]:
        """Extrait les indicateurs favorisant TRANSFERT."""
        indicators = defaultdict(int)
        total = len(patterns)
        
        if total == 0:
            return {}
        
        for pattern in patterns:
            io_diff = pattern.get('io_differences', {})
            
            # Analyser les transformations
            raw_diff = io_diff.get('raw_differences', {})
            transformation_data = raw_diff.get('transformation_data', [])
            
            for transform in transformation_data:
                # Préservation de la taille
                size_ratio = transform.get('size_ratio', 1.0)
                if 0.8 <= size_ratio <= 1.2:
                    indicators['size_preserved'] += 1
                
                # Préservation de la couleur dominante
                if transform.get('dominant_color_preserved', False):
                    indicators['dominant_color_preserved'] += 1
                
                # Changement de densité faible
                density_change = abs(transform.get('density_change', 0))
                if density_change < 0.1:
                    indicators['low_density_change'] += 1
                
                # Type de transformation
                transform_type = transform.get('transformation_type', '')
                if 'modification' in transform_type or 'edit' in transform_type:
                    indicators['modification_transform'] += 1
        
        # Normaliser par le total
        return {k: v / total for k, v in indicators.items()}
    
    def _build_command_prediction_model(self) -> Dict[str, Any]:
        """Construit un modèle de prédiction des commandes basé sur les caractéristiques."""
        command_patterns = defaultdict(list)
        
        # Analyser tous les scénarios d'entraînement
        for agi_file in self.training_scenarios_dir.glob("*_TEST0_VALID.agi"):
            task_id = agi_file.stem.replace("_TEST0_VALID", "")
            
            try:
                with open(agi_file, 'r', encoding='utf-8') as f:
                    content = f.read().strip()
                
                # Extraire les commandes
                commands = self._extract_commands_from_agi(content)
                
                # Charger les caractéristiques
                characteristics = self._load_training_task_characteristics(task_id)
                if not characteristics:
                    continue
                
                # Associer commandes aux caractéristiques
                for command in commands:
                    command_patterns[command].append(characteristics)
                    
            except Exception as e:
                continue
        
        print(f"   📊 Analysé {len(command_patterns)} types de commandes")
        
        return command_patterns
    
    def _build_transformation_patterns(self) -> Dict[str, Any]:
        """Construit des patterns de transformation basés sur les différences IO."""
        patterns = defaultdict(list)
        
        for task_file in self.training_results_dir.glob("*_io_differences.json"):
            task_id = task_file.stem.replace("_io_differences", "")
            
            try:
                with open(task_file, 'r', encoding='utf-8') as f:
                    io_data = json.load(f)
                
                # Analyser les patterns de transformation
                raw_diff = io_data.get('raw_differences', {})
                transformation_data = raw_diff.get('transformation_data', [])
                
                for transform in transformation_data:
                    transform_type = transform.get('transformation_type', 'unknown')
                    patterns[transform_type].append({
                        'task_id': task_id,
                        'size_ratio': transform.get('size_ratio', 1.0),
                        'density_change': transform.get('density_change', 0),
                        'color_count_change': transform.get('color_count_change', 0)
                    })
                    
            except Exception as e:
                continue
        
        return dict(patterns)
    
    def _build_size_change_rules(self) -> Dict[str, List[str]]:
        """Construit des règles basées sur les changements de taille."""
        rules = {
            'expansion_large': ['RESIZE', 'MULTIPLY'],
            'expansion_small': ['RESIZE'],
            'reduction': ['EXTRACT', 'RESIZE'],
            'same_size': ['EDIT', 'FILL', 'FLIP', 'ROTATE'],
            'tiling': ['MULTIPLY', 'MOTIF']
        }
        
        return rules
    
    def _build_color_transformation_rules(self) -> Dict[str, List[str]]:
        """Construit des règles basées sur les transformations de couleur."""
        rules = {
            'color_increase': ['FILL', 'FLOODFILL', 'EDIT'],
            'color_decrease': ['REPLACE', 'CLEAR'],
            'color_preserved': ['MOVE', 'COPY', 'PASTE'],
            'color_pattern': ['MOTIF', 'PATTERN']
        }
        
        return rules
    
    def _load_training_task_characteristics(self, task_id: str) -> Dict[str, Any]:
        """Charge toutes les caractéristiques d'une tâche d'entraînement."""
        characteristics = {}
        
        # Fichiers à charger
        files_to_load = [
            f"{task_id}_io_differences.json",
            f"{task_id}_input_differences.json",
            f"{task_id}_output_differences.json",
            f"{task_id}_test0_input_params.json"
        ]
        
        # Ajouter les fichiers train
        for i in range(10):
            train_file = f"{task_id}_train{i}_input_params.json"
            if (self.training_results_dir / train_file).exists():
                files_to_load.append(train_file)
        
        for filename in files_to_load:
            file_path = self.training_results_dir / filename
            if file_path.exists():
                try:
                    with open(file_path, 'r', encoding='utf-8') as f:
                        data = json.load(f)
                        characteristics[filename.replace('.json', '')] = data
                except Exception as e:
                    continue
        
        return characteristics
    
    def _extract_commands_from_agi(self, content: str) -> List[str]:
        """Extrait les commandes d'un contenu AGI."""
        commands = []
        lines = content.strip().split('\n')
        
        for line in lines:
            line = line.strip()
            if not line or line.startswith('#') or line == 'END':
                continue
            
            # Extraire la commande principale
            if line.startswith('TRANSFERT'):
                commands.append('TRANSFERT')
                # Extraire les commandes dans TRANSFERT
                match = re.search(r'TRANSFERT\s*\{([^}]+)\}', line)
                if match:
                    inner_content = match.group(1)
                    inner_commands = self._extract_commands_from_agi(inner_content)
                    commands.extend(inner_commands)
            elif line.startswith('INIT'):
                commands.append('INIT')
            else:
                # Extraire le premier mot comme commande
                command = line.split()[0] if line.split() else ''
                if command:
                    commands.append(command)
        
        return commands

    def _load_evaluation_task_characteristics(self, task_id: str) -> Dict[str, Any]:
        """Charge toutes les caractéristiques d'une tâche d'évaluation."""
        characteristics = {}

        # Fichiers à charger
        files_to_load = [
            f"{task_id}_io_differences.json",
            f"{task_id}_input_differences.json",
            f"{task_id}_output_differences.json",
            f"{task_id}_test0_input_params.json"
        ]

        # Ajouter les fichiers train
        for i in range(10):
            train_file = f"{task_id}_train{i}_input_params.json"
            if (self.evaluation_results_dir / train_file).exists():
                files_to_load.append(train_file)

        for filename in files_to_load:
            file_path = self.evaluation_results_dir / filename
            if file_path.exists():
                try:
                    with open(file_path, 'r', encoding='utf-8') as f:
                        data = json.load(f)
                        characteristics[filename.replace('.json', '')] = data
                except Exception as e:
                    continue

        return characteristics

    def _predict_initialization_type(self, characteristics: Dict[str, Any]) -> str:
        """Prédit si utiliser INIT ou TRANSFERT basé sur les caractéristiques."""
        init_model = self.prediction_models['init_vs_transfert']

        # Calculer les scores pour INIT et TRANSFERT
        init_score = 0.0
        transfert_score = 0.0

        # Analyser les différences IO
        io_diff = characteristics.get(f'{list(characteristics.keys())[0].split("_")[0]}_io_differences', {})
        if io_diff:
            raw_diff = io_diff.get('raw_differences', {})
            transformation_data = raw_diff.get('transformation_data', [])

            for transform in transformation_data:
                size_ratio = transform.get('size_ratio', 1.0)
                density_change = abs(transform.get('density_change', 0))
                transform_type = transform.get('transformation_type', '')

                # Indicateurs INIT
                if size_ratio > 2.0:
                    init_score += init_model['init_indicators'].get('large_size_increase', 0) * 2
                elif size_ratio < 0.5:
                    init_score += init_model['init_indicators'].get('large_size_decrease', 0) * 2

                if 'expansion' in transform_type:
                    init_score += init_model['init_indicators'].get('expansion_transform', 0) * 1.5

                if density_change > 0.3:
                    init_score += init_model['init_indicators'].get('high_density_change', 0) * 1.2

                # Indicateurs TRANSFERT
                if 0.8 <= size_ratio <= 1.2:
                    transfert_score += init_model['transfert_indicators'].get('size_preserved', 0) * 2

                if transform.get('dominant_color_preserved', False):
                    transfert_score += init_model['transfert_indicators'].get('dominant_color_preserved', 0) * 1.5

                if density_change < 0.1:
                    transfert_score += init_model['transfert_indicators'].get('low_density_change', 0) * 1.2

                if 'modification' in transform_type:
                    transfert_score += init_model['transfert_indicators'].get('modification_transform', 0) * 1.3

        # Décision basée sur les scores
        if init_score > transfert_score:
            return 'INIT'
        else:
            return 'TRANSFERT'

    def _predict_commands(self, characteristics: Dict[str, Any], init_type: str) -> List[str]:
        """Prédit les commandes basées sur les caractéristiques et le type d'initialisation."""
        predicted_commands = []

        # Analyser les caractéristiques pour prédire les commandes
        io_diff = characteristics.get(f'{list(characteristics.keys())[0].split("_")[0]}_io_differences', {})
        test_params = characteristics.get(f'{list(characteristics.keys())[0].split("_")[0]}_test0_input_params', {})

        if not io_diff or not test_params:
            # Fallback vers des commandes de base
            return ['EDIT', 'FILL'] if init_type == 'TRANSFERT' else ['RESIZE', 'FILL']

        # Analyser les transformations
        raw_diff = io_diff.get('raw_differences', {})
        transformation_data = raw_diff.get('transformation_data', [])

        # Analyser les paramètres d'entrée
        grid_params = test_params.get('parameters', {})
        input_shape = [grid_params.get('height', 3), grid_params.get('width', 3)]
        color_count = grid_params.get('unique_colors_count', 2)
        density = grid_params.get('colored_density', 0.5)

        # Prédiction basée sur les transformations
        for transform in transformation_data:
            size_ratio = transform.get('size_ratio', 1.0)
            transform_type = transform.get('transformation_type', '')
            density_change = transform.get('density_change', 0)
            color_change = transform.get('color_count_change', 0)

            # Règles de prédiction basées sur la taille
            if size_ratio > 3.0:
                if 'MULTIPLY' not in predicted_commands:
                    predicted_commands.append('MULTIPLY')
                if 'RESIZE' not in predicted_commands:
                    predicted_commands.append('RESIZE')
            elif size_ratio > 1.5:
                if 'RESIZE' not in predicted_commands:
                    predicted_commands.append('RESIZE')
            elif size_ratio < 0.7:
                if 'EXTRACT' not in predicted_commands:
                    predicted_commands.append('EXTRACT')

            # Règles basées sur le type de transformation
            if 'tiling' in transform_type or 'expansion' in transform_type:
                if 'MOTIF' not in predicted_commands:
                    predicted_commands.append('MOTIF')
            elif 'modification' in transform_type:
                if 'EDIT' not in predicted_commands:
                    predicted_commands.append('EDIT')
            elif 'fill' in transform_type:
                if 'FILL' not in predicted_commands:
                    predicted_commands.append('FILL')

            # Règles basées sur les changements de couleur
            if color_change > 0:
                if 'FILL' not in predicted_commands:
                    predicted_commands.append('FILL')
            elif color_change < 0:
                if 'REPLACE' not in predicted_commands:
                    predicted_commands.append('REPLACE')

            # Règles basées sur les changements de densité
            if abs(density_change) > 0.3:
                if 'FLOODFILL' not in predicted_commands:
                    predicted_commands.append('FLOODFILL')

        # Règles basées sur les caractéristiques d'entrée
        if color_count <= 2:
            if 'FLIP' not in predicted_commands:
                predicted_commands.append('FLIP')
        elif color_count > 5:
            if 'FILL' not in predicted_commands:
                predicted_commands.append('FILL')

        if density < 0.3:
            if 'FILL' not in predicted_commands:
                predicted_commands.append('FILL')
        elif density > 0.7:
            if 'CLEAR' not in predicted_commands:
                predicted_commands.append('CLEAR')

        # Assurer un minimum de commandes
        if not predicted_commands:
            if init_type == 'TRANSFERT':
                predicted_commands = ['EDIT', 'FILL']
            else:
                predicted_commands = ['RESIZE', 'FILL']

        # Limiter le nombre de commandes
        return predicted_commands[:6]

    def _generate_enhanced_scenario_content(self, task_id: str, init_type: str,
                                          predicted_commands: List[str],
                                          characteristics: Dict[str, Any]) -> str:
        """Génère le contenu du scénario AGI amélioré."""
        lines = []

        # En-tête avec informations détaillées
        lines.append(f"# Scénario AGI amélioré pour la tâche {task_id}")
        lines.append(f"# Type d'initialisation: {init_type}")
        lines.append(f"# Commandes prédites: {', '.join(predicted_commands)}")
        lines.append(f"# Généré avec analyse complète des caractéristiques")
        lines.append("")

        # Analyser les paramètres pour l'initialisation
        test_params = characteristics.get(f'{task_id}_test0_input_params', {})
        grid_params = test_params.get('parameters', {})

        if init_type == 'INIT':
            # Déterminer la taille de grille pour INIT
            height = grid_params.get('height', 3)
            width = grid_params.get('width', 3)

            # Analyser les transformations pour prédire la taille de sortie
            io_diff = characteristics.get(f'{task_id}_io_differences', {})
            if io_diff:
                raw_diff = io_diff.get('raw_differences', {})
                transformation_data = raw_diff.get('transformation_data', [])

                for transform in transformation_data:
                    output_shape = transform.get('output_shape', [height, width])
                    if output_shape and len(output_shape) == 2:
                        height, width = output_shape
                        break

            lines.append(f"INIT {height}x{width}")
        else:
            lines.append("TRANSFERT")

        # Ajouter les commandes prédites
        for command in predicted_commands:
            lines.append(command)

        lines.append("END")

        return "\n".join(lines)

    def generate_enhanced_scenario_for_task(self, task_id: str) -> bool:
        """Génère un scénario AGI amélioré pour une tâche spécifique."""
        print(f"🔍 Génération du scénario amélioré pour la tâche {task_id}")

        # Charger les caractéristiques de la tâche
        characteristics = self._load_evaluation_task_characteristics(task_id)
        if not characteristics:
            print(f"❌ Aucune caractéristique trouvée pour {task_id}")
            return False

        # Prédire le type d'initialisation
        init_type = self._predict_initialization_type(characteristics)

        # Prédire les commandes
        predicted_commands = self._predict_commands(characteristics, init_type)

        if not predicted_commands:
            print(f"❌ Aucune commande prédite pour {task_id}")
            return False

        # Générer le contenu du scénario
        scenario_content = self._generate_enhanced_scenario_content(
            task_id, init_type, predicted_commands, characteristics
        )

        # Sauvegarder le scénario
        scenario_file = self.scenarios_dir / f"{task_id}_TEST0_ENHANCED.agi"
        try:
            with open(scenario_file, 'w', encoding='utf-8') as f:
                f.write(scenario_content)

            print(f"✅ Scénario généré: {scenario_file}")
            print(f"   Type d'initialisation: {init_type}")
            print(f"   Commandes prédites: {', '.join(predicted_commands)}")
            return True

        except Exception as e:
            print(f"❌ Erreur lors de la sauvegarde: {e}")
            return False

    def generate_all_enhanced_evaluation_scenarios(self) -> Dict[str, Any]:
        """Génère des scénarios améliorés pour toutes les tâches d'évaluation."""
        print("🚀 Génération des scénarios AGI améliorés pour toutes les tâches d'évaluation")
        print("=" * 80)

        # Trouver toutes les tâches d'évaluation
        evaluation_tasks = set()
        for file_path in self.evaluation_results_dir.glob("*_test0_input_params.json"):
            task_id = file_path.stem.replace("_test0_input_params", "")
            evaluation_tasks.add(task_id)

        evaluation_tasks = sorted(list(evaluation_tasks))
        print(f"📊 {len(evaluation_tasks)} tâches d'évaluation trouvées")

        # Statistiques de génération
        stats = {
            'total_tasks': len(evaluation_tasks),
            'successful_generations': 0,
            'failed_generations': 0,
            'init_count': 0,
            'transfert_count': 0,
            'command_distribution': Counter(),
            'initialization_distribution': Counter(),
            'generated_files': [],
            'detailed_results': []
        }

        # Générer les scénarios
        for i, task_id in enumerate(evaluation_tasks, 1):
            print(f"\n[{i:3d}/{len(evaluation_tasks)}] {task_id}")

            success = self.generate_enhanced_scenario_for_task(task_id)
            if success:
                stats['successful_generations'] += 1
                stats['generated_files'].append(f"{task_id}_TEST0_ENHANCED.agi")

                # Analyser les prédictions pour les statistiques
                characteristics = self._load_evaluation_task_characteristics(task_id)
                init_type = self._predict_initialization_type(characteristics)
                predicted_commands = self._predict_commands(characteristics, init_type)

                # Compter les types d'initialisation
                stats['initialization_distribution'][init_type] += 1
                if init_type == 'INIT':
                    stats['init_count'] += 1
                else:
                    stats['transfert_count'] += 1

                # Compter les commandes prédites
                for cmd in predicted_commands:
                    stats['command_distribution'][cmd] += 1

                # Détails pour analyse
                stats['detailed_results'].append({
                    'task_id': task_id,
                    'init_type': init_type,
                    'commands': predicted_commands,
                    'command_count': len(predicted_commands)
                })
            else:
                stats['failed_generations'] += 1

        # Afficher les statistiques finales
        self._print_enhanced_generation_stats(stats)

        # Sauvegarder les statistiques détaillées
        stats_file = self.scenarios_dir / "enhanced_generation_stats.json"
        with open(stats_file, 'w', encoding='utf-8') as f:
            # Convertir Counter en dict pour la sérialisation JSON
            stats_copy = stats.copy()
            stats_copy['command_distribution'] = dict(stats['command_distribution'])
            stats_copy['initialization_distribution'] = dict(stats['initialization_distribution'])
            json.dump(stats_copy, f, indent=2, ensure_ascii=False)

        return stats

    def _print_enhanced_generation_stats(self, stats: Dict[str, Any]) -> None:
        """Affiche les statistiques de génération améliorées."""
        print("\n" + "=" * 80)
        print("📈 STATISTIQUES DE GÉNÉRATION AMÉLIORÉE")
        print("=" * 80)

        print(f"✅ Scénarios générés avec succès: {stats['successful_generations']}")
        print(f"❌ Échecs de génération: {stats['failed_generations']}")
        print(f"📊 Taux de succès: {stats['successful_generations']/stats['total_tasks']*100:.1f}%")

        print(f"\n🎯 DISTRIBUTION DES TYPES D'INITIALISATION:")
        print("-" * 50)
        init_dist = stats['initialization_distribution']
        total_init = sum(init_dist.values())

        for init_type, count in init_dist.items():
            percentage = count / total_init * 100 if total_init > 0 else 0
            print(f"  {init_type:15s}: {count:3d} ({percentage:5.1f}%)")

        print(f"\n🎯 DISTRIBUTION DES COMMANDES PRÉDITES:")
        print("-" * 50)

        command_dist = stats['command_distribution']
        total_predictions = sum(command_dist.values())

        for command, count in command_dist.most_common():
            percentage = count / total_predictions * 100 if total_predictions > 0 else 0
            print(f"  {command:15s}: {count:3d} ({percentage:5.1f}%)")

        print(f"\n📁 Fichiers générés dans: {self.scenarios_dir}")
        print(f"   Total: {len(stats['generated_files'])} fichiers .agi")

        # Analyse de la diversité
        unique_commands = len(command_dist)
        avg_commands_per_task = total_predictions / stats['successful_generations'] if stats['successful_generations'] > 0 else 0

        print(f"\n📊 ANALYSE DE LA DIVERSITÉ:")
        print("-" * 30)
        print(f"  Types de commandes uniques: {unique_commands}")
        print(f"  Commandes moyennes par tâche: {avg_commands_per_task:.1f}")
        print(f"  Ratio INIT/TRANSFERT: {stats['init_count']}/{stats['transfert_count']}")

    def compare_with_basic_generator(self) -> None:
        """Compare les résultats avec le générateur de base."""
        print("\n🔄 COMPARAISON AVEC LE GÉNÉRATEUR DE BASE")
        print("-" * 60)

        basic_scenarios_dir = self.base_dir / "scenarios" / "evaluation"
        enhanced_scenarios_dir = self.scenarios_dir

        # Compter les fichiers
        basic_files = list(basic_scenarios_dir.glob("*_TEST0_PREDICTED.agi"))
        enhanced_files = list(enhanced_scenarios_dir.glob("*_TEST0_ENHANCED.agi"))

        print(f"📊 Générateur de base: {len(basic_files)} fichiers")
        print(f"📊 Générateur amélioré: {len(enhanced_files)} fichiers")

        # Analyser quelques exemples
        sample_tasks = [f.stem.replace("_TEST0_ENHANCED", "") for f in enhanced_files[:5]]

        for task_id in sample_tasks:
            basic_file = basic_scenarios_dir / f"{task_id}_TEST0_PREDICTED.agi"
            enhanced_file = enhanced_scenarios_dir / f"{task_id}_TEST0_ENHANCED.agi"

            if basic_file.exists() and enhanced_file.exists():
                print(f"\n📋 Comparaison pour {task_id}:")

                # Lire les contenus
                with open(basic_file, 'r', encoding='utf-8') as f:
                    basic_content = f.read().strip()
                with open(enhanced_file, 'r', encoding='utf-8') as f:
                    enhanced_content = f.read().strip()

                # Extraire les commandes
                basic_commands = self._extract_commands_from_agi(basic_content)
                enhanced_commands = self._extract_commands_from_agi(enhanced_content)

                print(f"   Base: {', '.join(basic_commands)}")
                print(f"   Amélioré: {', '.join(enhanced_commands)}")

                # Analyser les différences
                if basic_commands != enhanced_commands:
                    print(f"   ✨ Amélioration détectée!")
                else:
                    print(f"   ➡️  Même prédiction")


def main():
    """Point d'entrée principal."""
    print("🎯 GÉNÉRATEUR DE SCÉNARIOS AGI AMÉLIORÉ POUR L'ÉVALUATION")
    print("=" * 70)
    print("Utilise l'analyse complète des caractéristiques pour une prédiction précise")
    print("Détermine automatiquement INIT vs TRANSFERT et prédit les commandes optimales")
    print()

    # Initialiser le générateur
    generator = EnhancedEvaluationScenarioGenerator()

    print(f"✅ Modèles de prédiction construits:")
    print(f"   - Modèle INIT/TRANSFERT")
    print(f"   - Modèle de prédiction des commandes")
    print(f"   - Patterns de transformation")
    print(f"   - Règles de changement de taille")
    print(f"   - Règles de transformation des couleurs")

    # Générer tous les scénarios améliorés
    stats = generator.generate_all_enhanced_evaluation_scenarios()

    # Comparer avec le générateur de base
    generator.compare_with_basic_generator()

    print(f"\n🎉 GÉNÉRATION AMÉLIORÉE TERMINÉE")
    print(f"   Consultez les fichiers dans: {generator.scenarios_dir}")
    print(f"   Amélioration significative de la précision des prédictions!")


if __name__ == "__main__":
    main()
