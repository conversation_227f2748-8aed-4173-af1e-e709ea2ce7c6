# Analyse des Correspondances Caractéristiques-Scénarios ARC

## Résumé

- **Tâches analysées**: 318
- **Corrélations identifiées**: 300

## Fréquence des Commandes AGI

- **MOTIF**: 304 utilisations (95.6%)
- **PASTE**: 304 utilisations (95.6%)
- **EDIT**: 150 utilisations (47.2%)
- **CUT**: 123 utilisations (38.7%)
- **FILL**: 78 utilisations (24.5%)
- **COLOR**: 73 utilisations (23.0%)
- **RESIZE**: 26 utilisations (8.2%)
- **COPY**: 8 utilisations (2.5%)
- **MULTIPLY**: 6 utilisations (1.9%)
- **TRANSFERT**: 2 utilisations (0.6%)
- **FLIP**: 1 utilisations (0.3%)

## Corrélations Fortes

### dimension_change=(-5, -5)

Occurrences totales: 42

- **MOTIF**: 13 fois (30.95%) sur 304 utilisations totales
- **CUT**: 13 fois (30.95%) sur 123 utilisations totales
- **PASTE**: 13 fois (30.95%) sur 304 utilisations totales

### dimension_change=(3, 3)

Occurrences totales: 31

- **MOTIF**: 11 fois (35.48%) sur 304 utilisations totales
- **PASTE**: 11 fois (35.48%) sur 304 utilisations totales

### dimension_change=(3, 6)

Occurrences totales: 15

- **MOTIF**: 7 fois (46.67%) sur 304 utilisations totales
- **PASTE**: 7 fois (46.67%) sur 304 utilisations totales

### dimension_change=(5, 9)

Occurrences totales: 39

- **MOTIF**: 19 fois (48.72%) sur 304 utilisations totales
- **PASTE**: 19 fois (48.72%) sur 304 utilisations totales

### dimension_change=(6, 6)

Occurrences totales: 30

- **MOTIF**: 9 fois (30.0%) sur 304 utilisations totales
- **PASTE**: 9 fois (30.0%) sur 304 utilisations totales

### input_dimension_consistency>0

Occurrences totales: 604

- **MOTIF**: 182 fois (30.13%) sur 304 utilisations totales
- **PASTE**: 182 fois (30.13%) sur 304 utilisations totales

### input_shape=(1, 5)

Occurrences totales: 39

- **MOTIF**: 19 fois (48.72%) sur 304 utilisations totales
- **PASTE**: 19 fois (48.72%) sur 304 utilisations totales

### input_shape=(10, 7)

Occurrences totales: 24

- **MOTIF**: 8 fois (33.33%) sur 304 utilisations totales
- **CUT**: 8 fois (33.33%) sur 123 utilisations totales
- **PASTE**: 8 fois (33.33%) sur 304 utilisations totales

### input_shape=(11, 11)

Occurrences totales: 35

- **MOTIF**: 11 fois (31.43%) sur 304 utilisations totales
- **PASTE**: 11 fois (31.43%) sur 304 utilisations totales

### input_shape=(13, 13)

Occurrences totales: 40

- **MOTIF**: 12 fois (30.0%) sur 304 utilisations totales
- **PASTE**: 12 fois (30.0%) sur 304 utilisations totales

### input_shape=(14, 14)

Occurrences totales: 35

- **MOTIF**: 11 fois (31.43%) sur 304 utilisations totales
- **PASTE**: 11 fois (31.43%) sur 304 utilisations totales

### input_shape=(16, 16)

Occurrences totales: 16

- **MOTIF**: 8 fois (50.0%) sur 304 utilisations totales
- **PASTE**: 8 fois (50.0%) sur 304 utilisations totales

### input_shape=(18, 19)

Occurrences totales: 22

- **MOTIF**: 7 fois (31.82%) sur 304 utilisations totales
- **CUT**: 7 fois (31.82%) sur 123 utilisations totales
- **PASTE**: 7 fois (31.82%) sur 304 utilisations totales

### input_shape=(19, 19)

Occurrences totales: 23

- **MOTIF**: 10 fois (43.48%) sur 304 utilisations totales
- **PASTE**: 10 fois (43.48%) sur 304 utilisations totales

### input_shape=(21, 21)

Occurrences totales: 25

- **MOTIF**: 10 fois (40.0%) sur 304 utilisations totales
- **PASTE**: 10 fois (40.0%) sur 304 utilisations totales

### input_shape=(3, 6)

Occurrences totales: 16

- **MOTIF**: 7 fois (43.75%) sur 304 utilisations totales
- **PASTE**: 7 fois (43.75%) sur 304 utilisations totales

### input_shape=(30, 30)

Occurrences totales: 39

- **MOTIF**: 15 fois (38.46%) sur 304 utilisations totales
- **PASTE**: 15 fois (38.46%) sur 304 utilisations totales

### input_shape=(4, 4)

Occurrences totales: 18

- **MOTIF**: 6 fois (33.33%) sur 304 utilisations totales
- **CUT**: 6 fois (33.33%) sur 123 utilisations totales
- **PASTE**: 6 fois (33.33%) sur 304 utilisations totales

### output_shape=(10, 7)

Occurrences totales: 24

- **MOTIF**: 8 fois (33.33%) sur 304 utilisations totales
- **CUT**: 8 fois (33.33%) sur 123 utilisations totales
- **PASTE**: 8 fois (33.33%) sur 304 utilisations totales

### output_shape=(13, 13)

Occurrences totales: 19

- **MOTIF**: 7 fois (36.84%) sur 304 utilisations totales
- **PASTE**: 7 fois (36.84%) sur 304 utilisations totales

### output_shape=(18, 19)

Occurrences totales: 22

- **MOTIF**: 7 fois (31.82%) sur 304 utilisations totales
- **CUT**: 7 fois (31.82%) sur 123 utilisations totales
- **PASTE**: 7 fois (31.82%) sur 304 utilisations totales

### output_shape=(19, 19)

Occurrences totales: 17

- **MOTIF**: 8 fois (47.06%) sur 304 utilisations totales
- **PASTE**: 8 fois (47.06%) sur 304 utilisations totales

### output_shape=(21, 21)

Occurrences totales: 24

- **MOTIF**: 10 fois (41.67%) sur 304 utilisations totales
- **PASTE**: 10 fois (41.67%) sur 304 utilisations totales

### output_shape=(30, 30)

Occurrences totales: 39

- **MOTIF**: 15 fois (38.46%) sur 304 utilisations totales
- **PASTE**: 15 fois (38.46%) sur 304 utilisations totales

### output_shape=(9, 9)

Occurrences totales: 107

- **MOTIF**: 36 fois (33.64%) sur 304 utilisations totales
- **PASTE**: 36 fois (33.64%) sur 304 utilisations totales

### size_change>0

Occurrences totales: 189

- **MOTIF**: 67 fois (35.45%) sur 304 utilisations totales
- **PASTE**: 67 fois (35.45%) sur 304 utilisations totales

## Exemples de Correspondances

### Tâche 007bbfb7

**Scénario**: `RESIZE 9x9
MOTIF {CUT (COLOR 7 [0,0 2,2]); MULTIPLY 3 true; PASTE [0,0]}`

**Caractéristiques clés**:
- input_shape: (3, 3)
- output_shape: (9, 9)
- dimension_change: (6, 6)
- size_change: 72
- test0_input_params_height: 3

### Tâche 017c7c7b

**Scénario**: `RESIZE 3x9
MOTIF {COPY [3,0 5,2]; PASTE [6,0]}
REPLACE 1 2 [0,0 8,2]`

**Caractéristiques clés**:
- input_shape: (6, 3)
- output_shape: (9, 3)
- dimension_change: (0, 3)
- size_change: 9
- test0_input_params_height: 6

### Tâche 025d127b

**Scénario**: `MOTIF {CUT (COLOR 4 [0,0 4,8]); PASTE [1,2]}`

**Caractéristiques clés**:
- input_shape: (14, 9)
- output_shape: (14, 9)
- dimension_change: (0, 0)
- size_change: 0
- test0_input_params_height: 10

### Tâche 045e512c

**Scénario**: `MOTIF {COPY (COLOR 8 [7,6 9,8]); PASTE [7,10]}
REPLACE 8 2 [7,10 9,12]
MOTIF {COPY [7,10 9,12]; PASTE [7,14]; PASTE [7,18]}
MOTIF {COPY [7,18 9,20]; PASTE [3,10]}
REPLACE 2 4 [3,10 5,12]
MOTIF {COPY [3,10 5,12]; PASTE [11,6]}
REPLACE 4 3 [11,6 13,8]
MOTIF {COPY [11,6 13,8]; PASTE [15,6]}
EDITS {EDIT 3 ([20,6] [19,6] [19,7] [19,8] [20,8]); EDIT 4 ([1,14] [0,14] [0,16] [1,16])}`

**Caractéristiques clés**:
- input_shape: (21, 21)
- output_shape: (21, 21)
- dimension_change: (0, 0)
- size_change: 0
- test0_input_params_height: 21

### Tâche 0520fde7

**Scénario**: `MOTIF {COPY (COLOR 0 [0,4 2,6]); PASTE [0,0]}
EXTRACT [0,0 2,2]
REPLACE 1 2 [0,0 2,2]`

**Caractéristiques clés**:
- input_shape: (3, 7)
- output_shape: (3, 3)
- dimension_change: (-4, 0)
- size_change: -12
- test0_input_params_height: 3

