#!/usr/bin/env python3
"""
Analyseur de commandes amélioré pour les scénarios ARC.

Ce script analyse les scénarios AGI avec une classification plus sophistiquée des commandes
selon les règles spécifiées :
- MOTIF + CUT/COPY + PASTE sans COLOR ni transformations -> MOVE
- MOTIF + CUT/COPY + PASTE avec transformations -> caractériser par transformation
- Ajouter MOTIF s'il y a COLOR
"""

import json
import os
import re
from pathlib import Path
from collections import defaultdict, Counter
from typing import Dict, List, Any, Tuple, Optional
import logging

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class EnhancedCommandAnalyzer:
    """Analyseur de commandes amélioré avec classification sophistiquée."""
    
    def __init__(self, scenarios_dir: str, results_dir: str):
        self.scenarios_dir = Path(scenarios_dir)
        self.results_dir = Path(results_dir)
        self.enhanced_commands = {}
        self.command_patterns = defaultdict(list)
        self.transformation_stats = Counter()
        
    def parse_motif_command(self, motif_content: str) -> Dict[str, Any]:
        """Parse le contenu d'une commande MOTIF pour identifier ses composants."""
        components = {
            'has_cut': False,
            'has_copy': False,
            'has_paste': False,
            'has_color': False,
            'transformations': [],
            'raw_content': motif_content
        }
        
        # Nettoyer le contenu
        content = motif_content.strip()
        if content.startswith('MOTIF {') and content.endswith('}'):
            content = content[7:-1].strip()
        elif content.startswith('{') and content.endswith('}'):
            content = content[1:-1].strip()
        
        # Détecter les composants de base
        if 'CUT' in content:
            components['has_cut'] = True
        if 'COPY' in content:
            components['has_copy'] = True
        if 'PASTE' in content:
            components['has_paste'] = True
        if 'COLOR' in content:
            components['has_color'] = True
        
        # Détecter les transformations
        transformations = []
        
        # Rotations
        if 'ROTATE RIGHT' in content:
            transformations.append('ROTATE RIGHT')
        elif 'ROTATE LEFT' in content:
            transformations.append('ROTATE LEFT')
        elif 'ROTATE' in content:
            transformations.append('ROTATE')
        
        # Flips
        if 'FLIP VERTICAL' in content:
            transformations.append('FLIP VERTICAL')
        elif 'FLIP HORIZONTAL' in content:
            transformations.append('FLIP HORIZONTAL')
        elif 'FLIP' in content:
            transformations.append('FLIP')
        
        # Autres transformations
        if 'DIVIDE' in content:
            # Extraire le nombre de division
            divide_match = re.search(r'DIVIDE\s+(\d+)', content)
            if divide_match:
                transformations.append(f'DIVIDE {divide_match.group(1)}')
            else:
                transformations.append('DIVIDE')
        
        if 'MULTIPLY' in content:
            # Extraire les paramètres de multiplication
            multiply_match = re.search(r'MULTIPLY\s+(\d+)\s+(true|false)', content)
            if multiply_match:
                transformations.append(f'MULTIPLY {multiply_match.group(1)} {multiply_match.group(2)}')
            else:
                transformations.append('MULTIPLY')
        
        if 'SCALE' in content:
            transformations.append('SCALE')
        
        if 'MIRROR' in content:
            transformations.append('MIRROR')
        
        components['transformations'] = transformations
        
        return components
    
    def classify_motif_command(self, motif_content: str) -> str:
        """Classifie une commande MOTIF selon les règles spécifiées."""
        components = self.parse_motif_command(motif_content)
        
        # Vérifier si c'est un pattern de base (CUT/COPY + PASTE)
        has_basic_pattern = (components['has_cut'] or components['has_copy']) and components['has_paste']
        
        if not has_basic_pattern:
            return 'MOTIF'  # Commande MOTIF standard
        
        # Si pas de transformations et pas de COLOR -> MOVE
        if not components['transformations'] and not components['has_color']:
            return 'MOVE'
        
        # Si pas de transformations mais avec COLOR -> MOVE MOTIF
        if not components['transformations'] and components['has_color']:
            return 'MOVE MOTIF'
        
        # Si des transformations sont présentes
        if components['transformations']:
            transformation_desc = ' AND '.join(components['transformations'])
            if components['has_color']:
                return f'{transformation_desc} MOTIF'
            else:
                return transformation_desc
        
        # Cas par défaut
        return 'MOTIF'
    
    def extract_enhanced_commands_from_scenario(self, scenario: str) -> List[str]:
        """Extrait les commandes avec la classification améliorée."""
        if not scenario:
            return []
        
        commands = []
        lines = scenario.split('\n')
        
        for line in lines:
            line = line.strip()
            if not line:
                continue
            
            # Phase d'initialisation
            if line in ['INIT', 'TRANSFERT']:
                commands.append(line)
            
            # Commandes déjà classifiées par le nettoyeur
            elif line == 'MOVE':
                commands.append('MOVE')
            elif line == 'MOVE MOTIF':
                commands.append('MOVE MOTIF')
            elif line.endswith(' MOTIF'):
                # Transformations avec MOTIF (ex: FLIP MOTIF, ROTATE MOTIF)
                commands.append(line)
                self.transformation_stats[line] += 1
            elif line in ['FLIP', 'ROTATE', 'DIVIDE', 'MULTIPLY', 'SCALE', 'MIRROR']:
                # Transformations sans MOTIF
                commands.append(line)
                self.transformation_stats[line] += 1
            elif 'FLIP' in line or 'ROTATE' in line or 'DIVIDE' in line or 'MULTIPLY' in line:
                # Transformations multiples (ex: FLIP ROTATE MOTIF)
                commands.append(line)
                self.transformation_stats[line] += 1
            
            # Commandes groupées
            elif line == 'EDITS':
                commands.append('EDITS')
            elif line == 'REPLACES':
                commands.append('REPLACES')
            elif line == 'FLOODFILLS':
                commands.append('FLOODFILLS')
            elif line == 'SURROUNDS':
                commands.append('SURROUNDS')
            elif line == 'CLEARS':
                commands.append('CLEARS')
            
            # Commandes individuelles
            elif line == 'FLOODFILL':
                commands.append('FLOODFILL')
            elif line == 'SURROUND':
                commands.append('SURROUND')
            elif line == 'CLEAR':
                commands.append('CLEAR')
            elif line == 'CLEAR INVERT':
                commands.append('CLEAR INVERT')
            elif line == 'EDIT':
                commands.append('EDIT')
            elif line == 'REPLACE':
                commands.append('REPLACE')
            elif line == 'FILL':
                commands.append('FILL')
            elif line == 'RESIZE':
                commands.append('RESIZE')
            elif line == 'EXTRACT':
                commands.append('EXTRACT')
            
            # Traitement spécial pour MOTIF (cas où le nettoyeur n'a pas traité)
            elif line.startswith('MOTIF'):
                classified_command = self.classify_motif_command(line)
                commands.append(classified_command)
                
                # Mettre à jour les statistiques de transformation
                if classified_command not in ['MOVE', 'MOVE MOTIF', 'MOTIF']:
                    self.transformation_stats[classified_command] += 1
            
            # Autres commandes non reconnues
            else:
                # Extraire le nom de la commande (premier mot)
                command_name = line.split()[0] if line.split() else line
                commands.append(command_name)
        
        return commands
    
    def analyze_enhanced_correlations(self):
        """Analyse les corrélations avec la classification améliorée."""
        logger.info("Début de l'analyse des corrélations améliorées...")
        
        # Obtenir la liste des tâches
        task_ids = set()
        for file_path in self.scenarios_dir.glob("*_TEST0_REDUCED.agi"):
            task_id = file_path.stem.replace('_TEST0_REDUCED', '')
            task_ids.add(task_id)
        
        logger.info(f"Trouvé {len(task_ids)} tâches à analyser")
        
        processed_tasks = 0
        enhanced_command_stats = Counter()
        
        for task_id in sorted(task_ids):
            # Charger le scénario
            scenario_path = self.scenarios_dir / f"{task_id}_TEST0_REDUCED.agi"
            if not scenario_path.exists():
                continue
            
            try:
                with open(scenario_path, 'r', encoding='utf-8') as f:
                    scenario = f.read().strip()
            except Exception as e:
                logger.warning(f"Erreur lors du chargement du scénario {scenario_path}: {e}")
                continue
            
            # Extraire les commandes améliorées
            enhanced_commands = self.extract_enhanced_commands_from_scenario(scenario)
            if not enhanced_commands:
                continue
            
            # Charger les caractéristiques (réutiliser la logique existante)
            characteristics = self.load_task_characteristics(task_id)
            if not characteristics:
                continue
            
            # Stocker les données
            self.enhanced_commands[task_id] = {
                'scenario': scenario,
                'enhanced_commands': enhanced_commands,
                'characteristics': characteristics
            }
            
            # Mettre à jour les statistiques
            for command in enhanced_commands:
                enhanced_command_stats[command] += 1
            
            processed_tasks += 1
            if processed_tasks % 50 == 0:
                logger.info(f"Traité {processed_tasks} tâches...")
        
        logger.info(f"Analyse terminée. {processed_tasks} tâches traitées.")
        
        return enhanced_command_stats
    
    def load_task_characteristics(self, task_id: str) -> Dict[str, Any]:
        """Charge les caractéristiques d'une tâche (réutilise la logique existante)."""
        characteristics = {}
        
        file_types = [
            'io_differences',
            'input_differences', 
            'output_differences',
            'test0_input_params',
            'train0_input_params',
            'train1_input_params',
            'train2_input_params'
        ]
        
        for file_type in file_types:
            file_path = self.results_dir / f"{task_id}_{file_type}.json"
            if file_path.exists():
                try:
                    with open(file_path, 'r', encoding='utf-8') as f:
                        characteristics[file_type] = json.load(f)
                except Exception as e:
                    logger.warning(f"Erreur lors du chargement de {file_path}: {e}")
        
        return characteristics
    
    def generate_enhanced_analysis_report(self, enhanced_command_stats: Counter) -> Dict[str, Any]:
        """Génère un rapport d'analyse amélioré."""
        total_tasks = len(self.enhanced_commands)
        
        report = {
            'metadata': {
                'total_tasks_analyzed': total_tasks,
                'total_enhanced_commands': len(enhanced_command_stats),
                'analysis_type': 'enhanced_command_classification'
            },
            'enhanced_command_frequency': dict(enhanced_command_stats.most_common()),
            'command_categories': {
                'movement_operations': {},
                'transformation_operations': {},
                'basic_operations': {},
                'specialized_operations': {}
            },
            'transformation_breakdown': dict(self.transformation_stats.most_common()),
            'examples': {}
        }
        
        # Catégoriser les commandes
        for command, count in enhanced_command_stats.items():
            percentage = (count / total_tasks) * 100
            
            if command in ['MOVE', 'MOVE MOTIF']:
                report['command_categories']['movement_operations'][command] = {
                    'count': count,
                    'percentage': round(percentage, 2)
                }
            elif any(transform in command for transform in ['ROTATE', 'FLIP', 'DIVIDE', 'MULTIPLY', 'SCALE', 'MIRROR']):
                report['command_categories']['transformation_operations'][command] = {
                    'count': count,
                    'percentage': round(percentage, 2)
                }
            elif command in ['RESIZE', 'TRANSFERT', 'FILL', 'EDIT', 'EDITS', 'REPLACE', 'EXTRACT']:
                report['command_categories']['basic_operations'][command] = {
                    'count': count,
                    'percentage': round(percentage, 2)
                }
            else:
                report['command_categories']['specialized_operations'][command] = {
                    'count': count,
                    'percentage': round(percentage, 2)
                }
        
        # Ajouter des exemples
        example_count = 0
        for task_id, task_data in self.enhanced_commands.items():
            if example_count >= 10:
                break
            
            report['examples'][task_id] = {
                'scenario': task_data['scenario'][:200] + '...' if len(task_data['scenario']) > 200 else task_data['scenario'],
                'enhanced_commands': task_data['enhanced_commands']
            }
            example_count += 1
        
        return report
    
    def save_enhanced_analysis(self, output_dir: str):
        """Sauvegarde l'analyse améliorée."""
        output_path = Path(output_dir)
        output_path.mkdir(exist_ok=True)
        
        # Analyser avec la classification améliorée
        enhanced_command_stats = self.analyze_enhanced_correlations()
        
        # Générer le rapport
        report = self.generate_enhanced_analysis_report(enhanced_command_stats)
        
        # Sauvegarder le rapport JSON
        with open(output_path / 'enhanced_command_analysis.json', 'w', encoding='utf-8') as f:
            json.dump(report, f, indent=2, ensure_ascii=False)
        
        # Générer le rapport markdown
        self.generate_enhanced_markdown_report(output_path / 'enhanced_analysis_report.md', report)
        
        # Sauvegarder les données détaillées des commandes
        with open(output_path / 'enhanced_command_data.json', 'w', encoding='utf-8') as f:
            json.dump(self.enhanced_commands, f, indent=2, ensure_ascii=False)
        
        logger.info(f"Analyse améliorée sauvegardée dans {output_path}")
        
        return report
    
    def generate_enhanced_markdown_report(self, output_path: Path, report: Dict[str, Any]):
        """Génère un rapport markdown pour l'analyse améliorée."""
        with open(output_path, 'w', encoding='utf-8') as f:
            f.write("# Analyse Améliorée des Commandes ARC\n\n")
            
            # Métadonnées
            metadata = report['metadata']
            f.write("## Métadonnées\n\n")
            f.write(f"- **Tâches analysées**: {metadata['total_tasks_analyzed']}\n")
            f.write(f"- **Commandes améliorées identifiées**: {metadata['total_enhanced_commands']}\n")
            f.write(f"- **Type d'analyse**: {metadata['analysis_type']}\n\n")
            
            # Fréquence des commandes améliorées
            f.write("## Fréquence des Commandes Améliorées\n\n")
            for command, count in list(report['enhanced_command_frequency'].items())[:15]:
                percentage = (count / metadata['total_tasks_analyzed']) * 100
                f.write(f"- **{command}**: {count} utilisations ({percentage:.1f}%)\n")
            f.write("\n")
            
            # Catégories de commandes
            f.write("## Catégories de Commandes\n\n")
            categories = report['command_categories']
            
            for category_name, commands in categories.items():
                if commands:
                    f.write(f"### {category_name.replace('_', ' ').title()}\n\n")
                    for command, data in sorted(commands.items(), key=lambda x: x[1]['count'], reverse=True):
                        f.write(f"- **{command}**: {data['count']} fois ({data['percentage']}%)\n")
                    f.write("\n")
            
            # Analyse des transformations
            f.write("## Analyse des Transformations\n\n")
            if report['transformation_breakdown']:
                f.write("### Transformations les Plus Fréquentes\n\n")
                for transform, count in list(report['transformation_breakdown'].items())[:10]:
                    f.write(f"- **{transform}**: {count} utilisations\n")
                f.write("\n")
            
            # Exemples
            f.write("## Exemples de Classification\n\n")
            for task_id, example in list(report['examples'].items())[:5]:
                f.write(f"### Tâche {task_id}\n\n")
                f.write(f"**Scénario original**:\n```\n{example['scenario']}\n```\n\n")
                f.write(f"**Commandes classifiées**: {', '.join(example['enhanced_commands'])}\n\n")
            
            # Insights
            f.write("## Insights de la Classification Améliorée\n\n")
            
            # Calculer les insights
            movement_ops = sum(data['count'] for data in categories['movement_operations'].values())
            transform_ops = sum(data['count'] for data in categories['transformation_operations'].values())
            basic_ops = sum(data['count'] for data in categories['basic_operations'].values())
            
            total_ops = movement_ops + transform_ops + basic_ops
            
            if total_ops > 0:
                f.write(f"- **Opérations de mouvement**: {movement_ops} ({(movement_ops/total_ops)*100:.1f}%)\n")
                f.write(f"- **Opérations de transformation**: {transform_ops} ({(transform_ops/total_ops)*100:.1f}%)\n")
                f.write(f"- **Opérations de base**: {basic_ops} ({(basic_ops/total_ops)*100:.1f}%)\n\n")
            
            # Recommandations
            f.write("## Recommandations Basées sur la Classification Améliorée\n\n")
            f.write("### Pour le Développement de Modèles\n\n")
            f.write("- Distinguer clairement les opérations de mouvement (MOVE) des transformations complexes\n")
            f.write("- Développer des modules spécialisés pour chaque type de transformation\n")
            f.write("- Prioriser l'apprentissage des patterns de mouvement simples\n\n")
            
            f.write("### Pour l'Optimisation des Performances\n\n")
            f.write("- Implémenter des chemins d'exécution optimisés pour les opérations MOVE\n")
            f.write("- Créer une hiérarchie de complexité basée sur les types de transformation\n")
            f.write("- Développer des heuristiques spécifiques pour chaque catégorie d'opération\n")

def main():
    """Fonction principale."""
    scenarios_dir = "src/grid_analysis/scenarios/training/reduced"
    results_dir = "src/grid_analysis/results/training"
    output_dir = "src/grid_analysis/enhanced_analysis"
    
    # Vérifier que les répertoires existent
    if not Path(scenarios_dir).exists():
        logger.error(f"Répertoire des scénarios non trouvé: {scenarios_dir}")
        return
    
    if not Path(results_dir).exists():
        logger.error(f"Répertoire des résultats non trouvé: {results_dir}")
        return
    
    # Créer l'analyseur et lancer l'analyse
    analyzer = EnhancedCommandAnalyzer(scenarios_dir, results_dir)
    report = analyzer.save_enhanced_analysis(output_dir)
    
    # Afficher un résumé
    print("\n" + "="*60)
    print("ANALYSE AMÉLIORÉE DES COMMANDES ARC")
    print("="*60)
    print(f"📊 Tâches analysées: {report['metadata']['total_tasks_analyzed']}")
    print(f"🔧 Commandes améliorées: {report['metadata']['total_enhanced_commands']}")
    
    # Top 5 des commandes
    print("\n🎯 Top 5 des commandes:")
    for i, (command, count) in enumerate(list(report['enhanced_command_frequency'].items())[:5]):
        percentage = (count / report['metadata']['total_tasks_analyzed']) * 100
        print(f"   {i+1}. {command}: {count} ({percentage:.1f}%)")
    
    print(f"\n📁 Résultats sauvegardés dans: {output_dir}")
    print("="*60)
    
    logger.info("Analyse améliorée terminée avec succès!")

if __name__ == "__main__":
    main()