#!/usr/bin/env python3
"""
Test du nettoyeur de scénarios amélioré.
"""

import sys
import os
sys.path.append('src/grid_analysis/scenarios')
from scenario_cleaner import clean_agi_scenario

def test_scenarios():
    """Teste différents types de scénarios."""
    
    test_cases = [
        # Test 1: MOTIF simple sans COLOR ni transformation -> MOVE
        {
            'name': 'MOTIF simple -> MOVE',
            'input': '''TRANSFERT {INIT 3x3}
MOTIF {COPY [4,0 7,3]; PASTE [0,0]}
END''',
            'expected_output': '''TRANSFERT
MOVE'''
        },
        
        # Test 2: MOTIF avec COLOR -> MOVE MOTIF
        {
            'name': 'MOTIF avec COLOR -> MOVE MOTIF',
            'input': '''INIT 3x3
MOTIF {COPY (COLOR 9 [4,4 7,7]); PASTE [0,0]}
END''',
            'expected_output': '''INIT
MOVE MOTIF'''
        },
        
        # Test 3: MOTIF avec transformation -> FLIP
        {
            'name': 'MOTIF avec FLIP -> FLIP',
            'input': '''TRANSFERT {INIT 6x6}
MOTIF {CUT [0,0 6,6]; FLIP VERTICAL; PASTE [0,0]}
END''',
            'expected_output': '''TRANSFERT
FLIP'''
        },
        
        # Test 4: MOTIF avec transformation et COLOR -> ROTATE MOTIF
        {
            'name': 'MOTIF avec ROTATE et COLOR -> ROTATE MOTIF',
            'input': '''INIT 5x5
MOTIF {COPY (COLOR 1,3,4,2 [2,2 5,5]); ROTATE RIGHT; PASTE [3,10]}
END''',
            'expected_output': '''INIT
ROTATE MOTIF'''
        },
        
        # Test 5: Commandes groupées
        {
            'name': 'Commandes groupées',
            'input': '''TRANSFERT {INIT 10x10}
EDITS {EDIT 4 ([2,1] [2,3]); EDIT 7 ([1,6] [2,5])}
FLOODFILLS {FLOODFILL 9 [1,0]; FLOODFILL 5 [1,1]}
END''',
            'expected_output': '''TRANSFERT
EDITS
FLOODFILLS'''
        },
        
        # Test 6: Commandes individuelles
        {
            'name': 'Commandes individuelles',
            'input': '''INIT 8x8
FLOODFILL 3 [7,3]
SURROUND 8 [1,1 5,4]
CLEAR [0,0 2,2]
CLEAR (INVERT [10,5 11,10])
END''',
            'expected_output': '''INIT
FLOODFILL
SURROUND
CLEAR
CLEAR INVERT'''
        },
        
        # Test 7: Transformations multiples
        {
            'name': 'Transformations multiples',
            'input': '''TRANSFERT {INIT 12x12}
MOTIF {CUT (COLOR 5,2,4,1 [8,7 14,16]); FLIP VERTICAL; ROTATE RIGHT; PASTE [12,2]}
END''',
            'expected_output': '''TRANSFERT
FLIP ROTATE MOTIF'''
        },
        
        # Test 8: DIVIDE
        {
            'name': 'MOTIF avec DIVIDE',
            'input': '''INIT 15x15
MOTIF {CUT [1,2 12,13]; DIVIDE 3; PASTE [1,2]}
END''',
            'expected_output': '''INIT
DIVIDE'''
        }
    ]
    
    print("🧪 Test du Nettoyeur de Scénarios Amélioré")
    print("=" * 50)
    
    passed = 0
    failed = 0
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\nTest {i}: {test_case['name']}")
        print("-" * 40)
        
        # Nettoyer le scénario
        cleaned_content, errors = clean_agi_scenario(test_case['input'], f"test_{i}")
        
        if errors:
            print(f"❌ ÉCHEC - Erreurs: {errors}")
            failed += 1
            continue
        
        # Comparer avec le résultat attendu
        expected = test_case['expected_output'].strip()
        actual = cleaned_content.strip()
        
        if actual == expected:
            print(f"✅ RÉUSSI")
            print(f"   Résultat: {actual}")
            passed += 1
        else:
            print(f"❌ ÉCHEC")
            print(f"   Attendu:  {expected}")
            print(f"   Obtenu:   {actual}")
            failed += 1
    
    print(f"\n📊 Résultats des Tests")
    print("=" * 30)
    print(f"✅ Tests réussis: {passed}")
    print(f"❌ Tests échoués: {failed}")
    print(f"📈 Taux de réussite: {(passed/(passed+failed))*100:.1f}%")
    
    return passed, failed

def test_real_scenario():
    """Teste avec un scénario réel."""
    print(f"\n🔍 Test avec Scénario Réel")
    print("=" * 30)
    
    # Scénario réel du fichier 6f8cd79b_TEST0_VALID.agi
    real_scenario = '''TRANSFERT {INIT 6x7}
SURROUND 8 [1,1 5,4]
END'''
    
    print("Scénario original:")
    for i, line in enumerate(real_scenario.split('\n'), 1):
        print(f"  {i}: {line}")
    
    cleaned_content, errors = clean_agi_scenario(real_scenario, "6f8cd79b")
    
    if errors:
        print(f"❌ Erreurs: {errors}")
    else:
        print(f"\nScénario nettoyé:")
        for i, line in enumerate(cleaned_content.split('\n'), 1):
            print(f"  {i}: {line}")

if __name__ == "__main__":
    # Tests unitaires
    passed, failed = test_scenarios()
    
    # Test avec scénario réel
    test_real_scenario()
    
    # Résultat final
    if failed == 0:
        print(f"\n🎉 Tous les tests sont passés ! Le nettoyeur fonctionne correctement.")
    else:
        print(f"\n⚠️  {failed} test(s) ont échoué. Vérifiez l'implémentation.")