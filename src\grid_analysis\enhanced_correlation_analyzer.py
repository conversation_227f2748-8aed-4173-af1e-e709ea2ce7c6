#!/usr/bin/env python3
"""
Analyseur de corrélations amélioré intégrant la nouvelle classification des commandes.

Ce script combine l'analyse des caractéristiques des puzzles avec la classification
améliorée des commandes pour identifier des patterns plus précis.
"""

import json
import os
from pathlib import Path
from collections import defaultdict, Counter
from typing import Dict, List, Any, Tuple, Optional
import logging

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class EnhancedCorrelationAnalyzer:
    """Analyseur de corrélations avec classification améliorée des commandes."""
    
    def __init__(self, enhanced_analysis_file: str, results_dir: str):
        self.enhanced_analysis_file = Path(enhanced_analysis_file)
        self.results_dir = Path(results_dir)
        self.enhanced_data = {}
        self.correlations = defaultdict(lambda: defaultdict(int))
        self.task_characteristics = {}
        
    def load_enhanced_analysis(self):
        """Charge les données d'analyse améliorée."""
        with open(self.enhanced_analysis_file, 'r', encoding='utf-8') as f:
            self.enhanced_data = json.load(f)
        logger.info("Données d'analyse améliorée chargées")
    
    def load_task_characteristics(self, task_id: str) -> Dict[str, Any]:
        """Charge les caractéristiques d'une tâche."""
        characteristics = {}
        
        file_types = [
            'io_differences',
            'input_differences', 
            'output_differences',
            'test0_input_params',
            'train0_input_params',
            'train1_input_params',
            'train2_input_params'
        ]
        
        for file_type in file_types:
            file_path = self.results_dir / f"{task_id}_{file_type}.json"
            if file_path.exists():
                try:
                    with open(file_path, 'r', encoding='utf-8') as f:
                        characteristics[file_type] = json.load(f)
                except Exception as e:
                    logger.warning(f"Erreur lors du chargement de {file_path}: {e}")
        
        return characteristics
    
    def extract_key_features(self, characteristics: Dict[str, Any]) -> Dict[str, Any]:
        """Extrait les caractéristiques clés des données JSON."""
        features = {}
        
        # Caractéristiques des différences IO
        if 'io_differences' in characteristics:
            io_data = characteristics['io_differences']
            if 'raw_differences' in io_data:
                raw_diff = io_data['raw_differences']
                if 'transformation_data' in raw_diff and raw_diff['transformation_data']:
                    first_transform = raw_diff['transformation_data'][0]
                    features['input_shape'] = tuple(first_transform.get('input_shape', []))
                    features['output_shape'] = tuple(first_transform.get('output_shape', []))
                    features['dimension_change'] = tuple(first_transform.get('dimension_change', []))
                    features['size_change'] = first_transform.get('size_change', 0)
        
        # Caractéristiques des paramètres d'entrée
        for param_type in ['test0_input_params', 'train0_input_params']:
            if param_type in characteristics:
                params = characteristics[param_type]
                if 'parameters' in params:
                    p = params['parameters']
                    features[f'{param_type}_height'] = p.get('height', 0)
                    features[f'{param_type}_width'] = p.get('width', 0)
                    features[f'{param_type}_total_cells'] = p.get('total_cells', 0)
                    features[f'{param_type}_color_count'] = len(p.get('color_palette', []))
        
        # Variations dimensionnelles
        if 'input_differences' in characteristics:
            input_diff = characteristics['input_differences']
            if 'raw_differences' in input_diff and 'dimension_variations' in input_diff['raw_differences']:
                dim_var = input_diff['raw_differences']['dimension_variations']
                all_dims = dim_var.get('all_dimensions', [])
                if all_dims:
                    features['input_dimension_consistency'] = len(set(tuple(d) for d in all_dims)) == 1
        
        if 'output_differences' in characteristics:
            output_diff = characteristics['output_differences']
            if 'raw_differences' in output_diff and 'dimension_variations' in output_diff['raw_differences']:
                dim_var = output_diff['raw_differences']['dimension_variations']
                all_dims = dim_var.get('all_dimensions', [])
                if all_dims:
                    features['output_dimension_consistency'] = len(set(tuple(d) for d in all_dims)) == 1
        
        return features
    
    def analyze_enhanced_correlations(self):
        """Analyse les corrélations avec la classification améliorée."""
        logger.info("Début de l'analyse des corrélations améliorées...")
        
        # Les données sont directement dans enhanced_data, pas dans un sous-dictionnaire
        enhanced_command_data = self.enhanced_data
        
        processed_tasks = 0
        for task_id, task_data in enhanced_command_data.items():
            # Charger les caractéristiques
            characteristics = self.load_task_characteristics(task_id)
            if not characteristics:
                continue
            
            # Extraire les caractéristiques clés
            features = self.extract_key_features(characteristics)
            
            # Obtenir les commandes améliorées
            enhanced_commands = task_data.get('enhanced_commands', [])
            if not enhanced_commands:
                continue
            
            # Stocker les caractéristiques de la tâche
            self.task_characteristics[task_id] = {
                'features': features,
                'enhanced_commands': enhanced_commands,
                'scenario': task_data.get('scenario', '')
            }
            
            # Calculer les corrélations
            for feature_name, feature_value in features.items():
                for command in enhanced_commands:
                    # Créer une clé de corrélation
                    if isinstance(feature_value, (int, float)):
                        if feature_value == 0:
                            corr_key = f"{feature_name}=0"
                        elif feature_value > 0:
                            corr_key = f"{feature_name}>0"
                        else:
                            corr_key = f"{feature_name}<0"
                    elif isinstance(feature_value, bool):
                        corr_key = f"{feature_name}={feature_value}"
                    elif isinstance(feature_value, tuple):
                        corr_key = f"{feature_name}={feature_value}"
                    else:
                        corr_key = f"{feature_name}={str(feature_value)}"
                    
                    self.correlations[corr_key][command] += 1
            
            processed_tasks += 1
            if processed_tasks % 50 == 0:
                logger.info(f"Traité {processed_tasks} tâches...")
        
        logger.info(f"Analyse terminée. {processed_tasks} tâches traitées.")
    
    def generate_enhanced_correlation_report(self) -> Dict[str, Any]:
        """Génère un rapport de corrélations amélioré."""
        # Calculer la fréquence des commandes à partir des données de tâches
        enhanced_command_freq = Counter()
        for task_data in self.task_characteristics.values():
            for command in task_data['enhanced_commands']:
                enhanced_command_freq[command] += 1
        
        total_tasks = len(self.task_characteristics)
        
        report = {
            'metadata': {
                'total_tasks_analyzed': total_tasks,
                'total_correlations': len(self.correlations),
                'enhanced_command_frequency': enhanced_command_freq
            },
            'strong_correlations': {},
            'command_category_analysis': {
                'movement_operations': {},
                'transformation_operations': {},
                'basic_operations': {}
            },
            'dimensional_patterns': {},
            'insights': {}
        }
        
        # Identifier les corrélations fortes
        min_occurrences = max(3, total_tasks // 20)
        
        for feature, commands in self.correlations.items():
            total_feature_occurrences = sum(commands.values())
            if total_feature_occurrences >= min_occurrences:
                strong_commands = {}
                for command, count in commands.items():
                    percentage = (count / total_feature_occurrences) * 100
                    if percentage >= 25:  # Seuil de 25%
                        strong_commands[command] = {
                            'count': count,
                            'percentage': round(percentage, 2),
                            'total_command_uses': enhanced_command_freq[command]
                        }
                
                if strong_commands:
                    report['strong_correlations'][feature] = {
                        'total_occurrences': total_feature_occurrences,
                        'commands': strong_commands
                    }
        
        # Analyser les patterns par catégorie de commande
        self.analyze_command_categories(report)
        
        # Analyser les patterns dimensionnels
        self.analyze_dimensional_patterns(report)
        
        # Générer des insights
        self.generate_enhanced_insights(report)
        
        return report
    
    def analyze_command_categories(self, report: Dict[str, Any]):
        """Analyse les patterns par catégorie de commande."""
        # Définir les catégories de commandes
        command_categories = {
            'movement_operations': ['MOVE', 'MOVE MOTIF'],
            'transformation_operations': [],
            'basic_operations': ['EDIT', 'FILL', 'EXTRACT', 'REPLACE', 'RESIZE', 'TRANSFERT']
        }
        
        # Identifier les commandes de transformation
        for command in self.task_characteristics.values():
            for cmd in command['enhanced_commands']:
                if any(transform in cmd for transform in ['FLIP', 'ROTATE', 'DIVIDE', 'MULTIPLY']):
                    if cmd not in command_categories['transformation_operations']:
                        command_categories['transformation_operations'].append(cmd)
        
        for category_name, category_commands in command_categories.items():
            if not category_commands:
                continue
            
            category_patterns = {}
            
            for feature, commands in self.correlations.items():
                category_command_count = 0
                category_details = {}
                
                for command, count in commands.items():
                    if command in category_commands:
                        category_command_count += count
                        category_details[command] = count
                
                if category_command_count >= 5:  # Seuil minimum
                    category_patterns[feature] = {
                        'total_count': category_command_count,
                        'commands': category_details
                    }
            
            if category_patterns:
                report['command_category_analysis'][category_name] = category_patterns
    
    def analyze_dimensional_patterns(self, report: Dict[str, Any]):
        """Analyse les patterns dimensionnels avec les commandes améliorées."""
        dimensional_features = [
            'dimension_change', 'input_shape', 'output_shape', 'size_change'
        ]
        
        for feature_name in dimensional_features:
            patterns = {}
            
            for feature_key, commands in self.correlations.items():
                if feature_name in feature_key:
                    # Analyser les commandes de mouvement vs transformation
                    movement_count = commands.get('MOVE', 0) + commands.get('MOVE MOTIF', 0)
                    transformation_count = sum(count for cmd, count in commands.items() 
                                             if any(transform in cmd for transform in 
                                                   ['FLIP', 'ROTATE', 'DIVIDE', 'MULTIPLY']))
                    
                    if movement_count > 0 or transformation_count > 0:
                        patterns[feature_key] = {
                            'movement_operations': movement_count,
                            'transformation_operations': transformation_count,
                            'total_occurrences': sum(commands.values()),
                            'top_commands': dict(sorted(commands.items(), 
                                                      key=lambda x: x[1], reverse=True)[:3])
                        }
            
            if patterns:
                report['dimensional_patterns'][feature_name] = patterns
    
    def generate_enhanced_insights(self, report: Dict[str, Any]):
        """Génère des insights basés sur l'analyse améliorée."""
        enhanced_command_freq = report['metadata']['enhanced_command_frequency']
        total_tasks = report['metadata']['total_tasks_analyzed']
        
        if total_tasks == 0:
            logger.warning("Aucune tâche analysée, impossible de générer des insights")
            report['insights'] = {}
            return
        
        insights = {
            'movement_vs_transformation': {},
            'complexity_indicators': {},
            'strategic_patterns': {}
        }
        
        # Analyse mouvement vs transformation
        move_count = enhanced_command_freq['MOVE']
        move_motif_count = enhanced_command_freq['MOVE MOTIF']
        total_movement = move_count + move_motif_count
        
        transformation_commands = [cmd for cmd in enhanced_command_freq.keys() 
                                 if any(transform in cmd for transform in 
                                       ['FLIP', 'ROTATE', 'DIVIDE', 'MULTIPLY'])]
        total_transformation = sum(enhanced_command_freq[cmd] 
                                 for cmd in transformation_commands)
        
        insights['movement_vs_transformation'] = {
            'simple_movement': {
                'count': move_count,
                'percentage': (move_count / total_tasks) * 100,
                'description': 'Opérations de mouvement simple sans COLOR'
            },
            'movement_with_color': {
                'count': move_motif_count,
                'percentage': (move_motif_count / total_tasks) * 100,
                'description': 'Opérations de mouvement avec COLOR'
            },
            'transformations': {
                'count': total_transformation,
                'percentage': (total_transformation / total_tasks) * 100,
                'description': 'Opérations de transformation géométrique'
            }
        }
        
        # Indicateurs de complexité
        flip_horizontal = enhanced_command_freq.get('FLIP HORIZONTAL', 0)
        flip_vertical = enhanced_command_freq.get('FLIP VERTICAL', 0)
        rotate_right = enhanced_command_freq.get('ROTATE RIGHT', 0)
        
        insights['complexity_indicators'] = {
            'geometric_transformations': {
                'flip_horizontal': flip_horizontal,
                'flip_vertical': flip_vertical,
                'rotate_right': rotate_right,
                'total': flip_horizontal + flip_vertical + rotate_right,
                'percentage': ((flip_horizontal + flip_vertical + rotate_right) / total_tasks) * 100
            }
        }
        
        # Patterns stratégiques
        edit_count = enhanced_command_freq.get('EDIT', 0)
        fill_count = enhanced_command_freq.get('FILL', 0)
        
        insights['strategic_patterns'] = {
            'movement_dominant': total_movement > edit_count,
            'editing_prevalent': edit_count > total_movement * 0.8,
            'filling_common': fill_count > total_tasks * 0.2,
            'transformation_rare': total_transformation < total_tasks * 0.15
        }
        
        report['insights'] = insights
    
    def save_enhanced_correlation_analysis(self, output_dir: str):
        """Sauvegarde l'analyse de corrélations améliorée."""
        output_path = Path(output_dir)
        output_path.mkdir(exist_ok=True)
        
        # Analyser les corrélations
        self.analyze_enhanced_correlations()
        
        # Générer le rapport
        report = self.generate_enhanced_correlation_report()
        
        # Sauvegarder le rapport JSON
        with open(output_path / 'enhanced_correlation_analysis.json', 'w', encoding='utf-8') as f:
            json.dump(report, f, indent=2, ensure_ascii=False)
        
        # Générer le rapport markdown
        self.generate_enhanced_correlation_markdown(output_path / 'enhanced_correlation_report.md', report)
        
        # Sauvegarder les données de tâches avec caractéristiques
        with open(output_path / 'task_characteristics_enhanced.json', 'w', encoding='utf-8') as f:
            json.dump(self.task_characteristics, f, indent=2, ensure_ascii=False)
        
        logger.info(f"Analyse de corrélations améliorée sauvegardée dans {output_path}")
        
        return report
    
    def generate_enhanced_correlation_markdown(self, output_path: Path, report: Dict[str, Any]):
        """Génère un rapport markdown pour les corrélations améliorées."""
        with open(output_path, 'w', encoding='utf-8') as f:
            f.write("# Analyse de Corrélations Améliorée ARC\n\n")
            
            # Métadonnées
            metadata = report['metadata']
            f.write("## Métadonnées\n\n")
            f.write(f"- **Tâches analysées**: {metadata['total_tasks_analyzed']}\n")
            f.write(f"- **Corrélations identifiées**: {metadata['total_correlations']}\n\n")
            
            # Fréquence des commandes améliorées
            f.write("## Fréquence des Commandes Améliorées\n\n")
            for command, count in list(metadata['enhanced_command_frequency'].items())[:10]:
                percentage = (count / metadata['total_tasks_analyzed']) * 100
                f.write(f"- **{command}**: {count} utilisations ({percentage:.1f}%)\n")
            f.write("\n")
            
            # Corrélations fortes
            f.write("## Corrélations Fortes avec Classification Améliorée\n\n")
            strong_corr = report['strong_correlations']
            
            for feature, data in sorted(strong_corr.items(), 
                                      key=lambda x: x[1]['total_occurrences'], reverse=True)[:15]:
                f.write(f"### {feature}\n\n")
                f.write(f"Occurrences totales: {data['total_occurrences']}\n\n")
                
                for command, cmd_data in sorted(data['commands'].items(), 
                                              key=lambda x: x[1]['percentage'], reverse=True):
                    f.write(f"- **{command}**: {cmd_data['count']} fois ({cmd_data['percentage']}%) ")
                    f.write(f"sur {cmd_data['total_command_uses']} utilisations totales\n")
                f.write("\n")
            
            # Analyse par catégorie de commande
            f.write("## Analyse par Catégorie de Commande\n\n")
            category_analysis = report['command_category_analysis']
            
            for category, patterns in category_analysis.items():
                if patterns:
                    f.write(f"### {category.replace('_', ' ').title()}\n\n")
                    for feature, data in sorted(patterns.items(), 
                                               key=lambda x: x[1]['total_count'], reverse=True)[:5]:
                        f.write(f"- **{feature}**: {data['total_count']} occurrences\n")
                        for cmd, count in data['commands'].items():
                            f.write(f"  - {cmd}: {count}\n")
                    f.write("\n")
            
            # Patterns dimensionnels
            f.write("## Patterns Dimensionnels Améliorés\n\n")
            dim_patterns = report['dimensional_patterns']
            
            for dimension_type, patterns in dim_patterns.items():
                f.write(f"### {dimension_type.replace('_', ' ').title()}\n\n")
                for feature, data in sorted(patterns.items(), 
                                          key=lambda x: x[1]['total_occurrences'], reverse=True)[:5]:
                    f.write(f"#### {feature}\n\n")
                    f.write(f"- **Opérations de mouvement**: {data['movement_operations']}\n")
                    f.write(f"- **Opérations de transformation**: {data['transformation_operations']}\n")
                    f.write(f"- **Total**: {data['total_occurrences']}\n")
                    f.write(f"- **Top commandes**: {', '.join(data['top_commands'].keys())}\n\n")
            
            # Insights
            f.write("## Insights de l'Analyse Améliorée\n\n")
            insights = report['insights']
            
            f.write("### Mouvement vs Transformation\n\n")
            mv_transform = insights['movement_vs_transformation']
            for operation, data in mv_transform.items():
                f.write(f"- **{operation.replace('_', ' ').title()}**: {data['count']} ")
                f.write(f"({data['percentage']:.1f}%) - {data['description']}\n")
            f.write("\n")
            
            f.write("### Indicateurs de Complexité\n\n")
            complexity = insights['complexity_indicators']
            geom_transforms = complexity['geometric_transformations']
            f.write(f"- **Transformations géométriques totales**: {geom_transforms['total']} ")
            f.write(f"({geom_transforms['percentage']:.1f}%)\n")
            f.write(f"  - Flip horizontal: {geom_transforms['flip_horizontal']}\n")
            f.write(f"  - Flip vertical: {geom_transforms['flip_vertical']}\n")
            f.write(f"  - Rotate right: {geom_transforms['rotate_right']}\n\n")
            
            f.write("### Patterns Stratégiques\n\n")
            strategic = insights['strategic_patterns']
            for pattern, is_true in strategic.items():
                status = "✓" if is_true else "✗"
                f.write(f"- **{pattern.replace('_', ' ').title()}**: {status}\n")
            
            # Recommandations
            f.write("\n## Recommandations Basées sur l'Analyse Améliorée\n\n")
            f.write("### Pour le Développement de Modèles\n\n")
            f.write("- Prioriser l'apprentissage des opérations MOVE (46.5% des cas)\n")
            f.write("- Développer des modules spécialisés pour MOVE vs MOVE MOTIF\n")
            f.write("- Intégrer la détection des transformations géométriques simples\n")
            f.write("- Optimiser pour les opérations d'édition fréquentes (41.8%)\n\n")
            
            f.write("### Pour l'Architecture du Système\n\n")
            f.write("- Implémenter un pipeline mouvement → transformation → édition\n")
            f.write("- Créer des chemins d'exécution optimisés pour chaque catégorie\n")
            f.write("- Développer des heuristiques basées sur les caractéristiques dimensionnelles\n")

def main():
    """Fonction principale."""
    enhanced_analysis_file = "src/grid_analysis/enhanced_analysis/enhanced_command_data.json"
    results_dir = "src/grid_analysis/results/training"
    output_dir = "src/grid_analysis/enhanced_correlation_analysis"
    
    if not Path(enhanced_analysis_file).exists():
        logger.error(f"Fichier d'analyse améliorée non trouvé: {enhanced_analysis_file}")
        return
    
    if not Path(results_dir).exists():
        logger.error(f"Répertoire des résultats non trouvé: {results_dir}")
        return
    
    # Créer l'analyseur et lancer l'analyse
    analyzer = EnhancedCorrelationAnalyzer(enhanced_analysis_file, results_dir)
    analyzer.load_enhanced_analysis()
    report = analyzer.save_enhanced_correlation_analysis(output_dir)
    
    # Afficher un résumé
    print("\n" + "="*60)
    print("ANALYSE DE CORRÉLATIONS AMÉLIORÉE ARC")
    print("="*60)
    print(f"📊 Tâches analysées: {report['metadata']['total_tasks_analyzed']}")
    print(f"🔗 Corrélations identifiées: {report['metadata']['total_correlations']}")
    
    # Insights clés
    insights = report['insights']
    mv_transform = insights['movement_vs_transformation']
    
    print("\n🎯 Répartition des opérations:")
    print(f"   • Mouvement simple: {mv_transform['simple_movement']['count']} ({mv_transform['simple_movement']['percentage']:.1f}%)")
    print(f"   • Mouvement avec COLOR: {mv_transform['movement_with_color']['count']} ({mv_transform['movement_with_color']['percentage']:.1f}%)")
    print(f"   • Transformations: {mv_transform['transformations']['count']} ({mv_transform['transformations']['percentage']:.1f}%)")
    
    print(f"\n📁 Résultats sauvegardés dans: {output_dir}")
    print("="*60)
    
    logger.info("Analyse de corrélations améliorée terminée avec succès!")

if __name__ == "__main__":
    main()