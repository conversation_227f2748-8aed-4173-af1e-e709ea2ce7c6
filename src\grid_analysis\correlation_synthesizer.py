#!/usr/bin/env python3
"""
Synthétiseur de règles de correspondance entre caractéristiques et commandes AGI.

Ce script analyse les corrélations identifiées pour générer des règles de correspondance
plus sophistiquées et des insights sur les patterns de résolution des puzzles ARC.
"""

import json
import os
from pathlib import Path
from collections import defaultdict, Counter
from typing import Dict, List, Any, Tuple, Optional
import logging

# Configuration du logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class CorrelationSynthesizer:
    """Synthétiseur de règles de correspondance."""
    
    def __init__(self, correlation_file: str):
        self.correlation_file = Path(correlation_file)
        self.correlations = {}
        self.rules = []
        self.insights = {}
        
    def load_correlations(self):
        """Charge les données de corrélation."""
        with open(self.correlation_file, 'r', encoding='utf-8') as f:
            self.correlations = json.load(f)
        logger.info(f"Corrélations chargées: {len(self.correlations.get('strong_correlations', {}))} corrélations fortes")
    
    def analyze_dimension_patterns(self) -> Dict[str, Any]:
        """Analyse les patterns liés aux dimensions."""
        dimension_rules = {
            'size_increase_patterns': [],
            'size_decrease_patterns': [],
            'dimension_preservation_patterns': [],
            'specific_size_patterns': []
        }
        
        strong_corr = self.correlations.get('strong_correlations', {})
        
        for feature, data in strong_corr.items():
            if 'dimension_change=' in feature:
                # Extraire les valeurs de changement de dimension
                dim_str = feature.split('=')[1]
                try:
                    # Nettoyer et évaluer la chaîne de dimension
                    dim_str = dim_str.strip('()')
                    if ',' in dim_str:
                        width_change, height_change = map(int, dim_str.split(', '))
                        
                        # Analyser le type de changement
                        if width_change > 0 or height_change > 0:
                            pattern = {
                                'feature': feature,
                                'dimension_change': (width_change, height_change),
                                'primary_commands': list(data['commands'].keys()),
                                'occurrences': data['total_occurrences']
                            }
                            dimension_rules['size_increase_patterns'].append(pattern)
                        elif width_change < 0 or height_change < 0:
                            pattern = {
                                'feature': feature,
                                'dimension_change': (width_change, height_change),
                                'primary_commands': list(data['commands'].keys()),
                                'occurrences': data['total_occurrences']
                            }
                            dimension_rules['size_decrease_patterns'].append(pattern)
                        else:
                            pattern = {
                                'feature': feature,
                                'dimension_change': (width_change, height_change),
                                'primary_commands': list(data['commands'].keys()),
                                'occurrences': data['total_occurrences']
                            }
                            dimension_rules['dimension_preservation_patterns'].append(pattern)
                except:
                    continue
            
            elif 'input_shape=' in feature or 'output_shape=' in feature:
                # Analyser les patterns de taille spécifique
                shape_str = feature.split('=')[1]
                try:
                    shape_str = shape_str.strip('()')
                    if ',' in shape_str:
                        width, height = map(int, shape_str.split(', '))
                        pattern = {
                            'feature': feature,
                            'shape': (width, height),
                            'primary_commands': list(data['commands'].keys()),
                            'occurrences': data['total_occurrences']
                        }
                        dimension_rules['specific_size_patterns'].append(pattern)
                except:
                    continue
        
        return dimension_rules
    
    def analyze_command_combinations(self) -> Dict[str, Any]:
        """Analyse les combinaisons de commandes fréquentes."""
        command_freq = self.correlations.get('summary', {}).get('command_frequency', {})
        
        # Analyser les commandes qui apparaissent souvent ensemble
        combinations = {
            'frequent_pairs': [],
            'command_hierarchy': {},
            'specialized_commands': []
        }
        
        # Identifier les commandes principales vs spécialisées
        total_tasks = self.correlations.get('summary', {}).get('total_tasks', 1)
        
        for command, count in command_freq.items():
            usage_rate = count / total_tasks
            if usage_rate > 0.8:
                combinations['command_hierarchy'][command] = 'core'  # Commandes de base
            elif usage_rate > 0.4:
                combinations['command_hierarchy'][command] = 'common'  # Commandes communes
            elif usage_rate > 0.1:
                combinations['command_hierarchy'][command] = 'specialized'  # Commandes spécialisées
            else:
                combinations['command_hierarchy'][command] = 'rare'  # Commandes rares
        
        # Analyser les paires fréquentes (MOTIF+PASTE, CUT+PASTE, etc.)
        strong_corr = self.correlations.get('strong_correlations', {})
        command_cooccurrence = defaultdict(lambda: defaultdict(int))
        
        for feature, data in strong_corr.items():
            commands = list(data['commands'].keys())
            for i, cmd1 in enumerate(commands):
                for cmd2 in commands[i+1:]:
                    command_cooccurrence[cmd1][cmd2] += data['total_occurrences']
                    command_cooccurrence[cmd2][cmd1] += data['total_occurrences']
        
        # Identifier les paires les plus fréquentes
        for cmd1, partners in command_cooccurrence.items():
            for cmd2, cooccur_count in partners.items():
                if cooccur_count > 50:  # Seuil arbitraire
                    combinations['frequent_pairs'].append({
                        'commands': [cmd1, cmd2],
                        'cooccurrence_count': cooccur_count
                    })
        
        return combinations
    
    def generate_transformation_rules(self) -> List[Dict[str, Any]]:
        """Génère des règles de transformation basées sur les patterns identifiés."""
        rules = []
        
        dimension_patterns = self.analyze_dimension_patterns()
        
        # Règles pour l'augmentation de taille
        for pattern in dimension_patterns['size_increase_patterns']:
            rule = {
                'type': 'size_increase',
                'condition': f"dimension_change == {pattern['dimension_change']}",
                'recommended_commands': pattern['primary_commands'],
                'confidence': pattern['occurrences'],
                'description': f"Quand les dimensions augmentent de {pattern['dimension_change']}, utiliser principalement {', '.join(pattern['primary_commands'])}"
            }
            rules.append(rule)
        
        # Règles pour la diminution de taille
        for pattern in dimension_patterns['size_decrease_patterns']:
            rule = {
                'type': 'size_decrease',
                'condition': f"dimension_change == {pattern['dimension_change']}",
                'recommended_commands': pattern['primary_commands'],
                'confidence': pattern['occurrences'],
                'description': f"Quand les dimensions diminuent de {pattern['dimension_change']}, utiliser principalement {', '.join(pattern['primary_commands'])}"
            }
            rules.append(rule)
        
        # Règles pour les tailles spécifiques
        for pattern in dimension_patterns['specific_size_patterns']:
            if pattern['occurrences'] > 20:  # Seuil de confiance
                rule = {
                    'type': 'specific_size',
                    'condition': f"shape == {pattern['shape']}",
                    'recommended_commands': pattern['primary_commands'],
                    'confidence': pattern['occurrences'],
                    'description': f"Pour les grilles de taille {pattern['shape']}, utiliser principalement {', '.join(pattern['primary_commands'])}"
                }
                rules.append(rule)
        
        return rules
    
    def generate_insights(self) -> Dict[str, Any]:
        """Génère des insights sur les patterns de résolution."""
        insights = {
            'dominant_strategies': {},
            'complexity_indicators': {},
            'transformation_types': {}
        }
        
        command_freq = self.correlations.get('summary', {}).get('command_frequency', {})
        total_tasks = self.correlations.get('summary', {}).get('total_tasks', 1)
        
        # Stratégies dominantes
        insights['dominant_strategies'] = {
            'motif_based_approach': {
                'percentage': (command_freq.get('MOTIF', 0) / total_tasks) * 100,
                'description': "La majorité des solutions utilisent une approche basée sur les motifs (MOTIF)"
            },
            'direct_editing': {
                'percentage': (command_freq.get('EDIT', 0) / total_tasks) * 100,
                'description': "L'édition directe est utilisée dans environ la moitié des cas"
            },
            'cutting_operations': {
                'percentage': (command_freq.get('CUT', 0) / total_tasks) * 100,
                'description': "Les opérations de découpe sont utilisées dans environ un tiers des cas"
            }
        }
        
        # Indicateurs de complexité
        resize_usage = (command_freq.get('RESIZE', 0) / total_tasks) * 100
        multiply_usage = (command_freq.get('MULTIPLY', 0) / total_tasks) * 100
        
        insights['complexity_indicators'] = {
            'simple_transformations': {
                'indicator': resize_usage < 10,
                'description': f"Peu de redimensionnements explicites ({resize_usage:.1f}%) suggère des transformations principalement in-place"
            },
            'pattern_multiplication': {
                'indicator': multiply_usage < 5,
                'description': f"Peu de multiplications ({multiply_usage:.1f}%) suggère des transformations principalement unitaires"
            }
        }
        
        # Types de transformation
        strong_corr = self.correlations.get('strong_correlations', {})
        size_change_positive = sum(1 for feature in strong_corr.keys() if 'size_change>0' in feature)
        size_change_zero = sum(1 for feature in strong_corr.keys() if 'size_change=0' in feature)
        
        insights['transformation_types'] = {
            'expansion_transformations': {
                'prevalence': 'high' if size_change_positive > 0 else 'low',
                'description': "Transformations qui agrandissent la grille"
            },
            'in_place_transformations': {
                'prevalence': 'high' if size_change_zero > 0 else 'low',
                'description': "Transformations qui préservent la taille de la grille"
            }
        }
        
        return insights
    
    def generate_comprehensive_report(self) -> Dict[str, Any]:
        """Génère un rapport complet avec règles et insights."""
        dimension_patterns = self.analyze_dimension_patterns()
        command_combinations = self.analyze_command_combinations()
        transformation_rules = self.generate_transformation_rules()
        insights = self.generate_insights()
        
        report = {
            'metadata': {
                'total_tasks_analyzed': self.correlations.get('summary', {}).get('total_tasks', 0),
                'strong_correlations_found': len(self.correlations.get('strong_correlations', {})),
                'rules_generated': len(transformation_rules)
            },
            'dimension_patterns': dimension_patterns,
            'command_analysis': command_combinations,
            'transformation_rules': transformation_rules,
            'insights': insights,
            'recommendations': {
                'for_model_training': [
                    "Prioriser l'apprentissage des commandes MOTIF et PASTE (utilisées dans >95% des cas)",
                    "Développer une compréhension fine des transformations dimensionnelles",
                    "Intégrer la détection de patterns de taille spécifique (9x9, 21x21, etc.)"
                ],
                'for_puzzle_solving': [
                    "Commencer par analyser les changements dimensionnels",
                    "Identifier si la transformation préserve ou modifie la taille",
                    "Utiliser MOTIF+PASTE comme stratégie de base",
                    "Recourir à CUT pour les réductions de taille",
                    "Utiliser EDIT pour les ajustements fins"
                ]
            }
        }
        
        return report
    
    def save_synthesis(self, output_dir: str):
        """Sauvegarde la synthèse complète."""
        output_path = Path(output_dir)
        output_path.mkdir(exist_ok=True)
        
        # Rapport complet
        comprehensive_report = self.generate_comprehensive_report()
        with open(output_path / 'comprehensive_analysis.json', 'w', encoding='utf-8') as f:
            json.dump(comprehensive_report, f, indent=2, ensure_ascii=False)
        
        # Règles de transformation seules (pour utilisation programmatique)
        rules = self.generate_transformation_rules()
        with open(output_path / 'transformation_rules.json', 'w', encoding='utf-8') as f:
            json.dump(rules, f, indent=2, ensure_ascii=False)
        
        # Rapport markdown détaillé
        self.generate_detailed_markdown_report(output_path / 'detailed_analysis_report.md', comprehensive_report)
        
        logger.info(f"Synthèse sauvegardée dans {output_path}")
    
    def generate_detailed_markdown_report(self, output_path: Path, report: Dict[str, Any]):
        """Génère un rapport markdown détaillé."""
        with open(output_path, 'w', encoding='utf-8') as f:
            f.write("# Synthèse Approfondie des Correspondances Caractéristiques-Scénarios ARC\n\n")
            
            # Métadonnées
            metadata = report['metadata']
            f.write("## Métadonnées de l'Analyse\n\n")
            f.write(f"- **Tâches analysées**: {metadata['total_tasks_analyzed']}\n")
            f.write(f"- **Corrélations fortes identifiées**: {metadata['strong_correlations_found']}\n")
            f.write(f"- **Règles de transformation générées**: {metadata['rules_generated']}\n\n")
            
            # Patterns dimensionnels
            f.write("## Patterns Dimensionnels\n\n")
            dim_patterns = report['dimension_patterns']
            
            f.write("### Patterns d'Augmentation de Taille\n\n")
            for pattern in dim_patterns['size_increase_patterns']:
                f.write(f"- **{pattern['dimension_change']}**: {pattern['occurrences']} occurrences, ")
                f.write(f"commandes principales: {', '.join(pattern['primary_commands'])}\n")
            
            f.write("\n### Patterns de Diminution de Taille\n\n")
            for pattern in dim_patterns['size_decrease_patterns']:
                f.write(f"- **{pattern['dimension_change']}**: {pattern['occurrences']} occurrences, ")
                f.write(f"commandes principales: {', '.join(pattern['primary_commands'])}\n")
            
            f.write("\n### Patterns de Tailles Spécifiques\n\n")
            for pattern in sorted(dim_patterns['specific_size_patterns'], 
                                key=lambda x: x['occurrences'], reverse=True)[:10]:
                f.write(f"- **{pattern['shape']}**: {pattern['occurrences']} occurrences, ")
                f.write(f"commandes principales: {', '.join(pattern['primary_commands'])}\n")
            
            # Analyse des commandes
            f.write("\n## Analyse des Commandes\n\n")
            cmd_analysis = report['command_analysis']
            
            f.write("### Hiérarchie des Commandes\n\n")
            hierarchy = cmd_analysis['command_hierarchy']
            for level in ['core', 'common', 'specialized', 'rare']:
                commands = [cmd for cmd, lvl in hierarchy.items() if lvl == level]
                if commands:
                    f.write(f"- **{level.capitalize()}**: {', '.join(commands)}\n")
            
            f.write("\n### Paires de Commandes Fréquentes\n\n")
            for pair in sorted(cmd_analysis['frequent_pairs'], 
                             key=lambda x: x['cooccurrence_count'], reverse=True)[:5]:
                f.write(f"- **{' + '.join(pair['commands'])}**: {pair['cooccurrence_count']} co-occurrences\n")
            
            # Règles de transformation
            f.write("\n## Règles de Transformation\n\n")
            rules = report['transformation_rules']
            
            for rule_type in ['size_increase', 'size_decrease', 'specific_size']:
                type_rules = [r for r in rules if r['type'] == rule_type]
                if type_rules:
                    f.write(f"### {rule_type.replace('_', ' ').title()}\n\n")
                    for rule in sorted(type_rules, key=lambda x: x['confidence'], reverse=True)[:5]:
                        f.write(f"- **Condition**: {rule['condition']}\n")
                        f.write(f"  - **Commandes recommandées**: {', '.join(rule['recommended_commands'])}\n")
                        f.write(f"  - **Confiance**: {rule['confidence']} occurrences\n")
                        f.write(f"  - **Description**: {rule['description']}\n\n")
            
            # Insights
            f.write("## Insights Stratégiques\n\n")
            insights = report['insights']
            
            f.write("### Stratégies Dominantes\n\n")
            for strategy, data in insights['dominant_strategies'].items():
                f.write(f"- **{strategy.replace('_', ' ').title()}**: {data['percentage']:.1f}%\n")
                f.write(f"  - {data['description']}\n")
            
            f.write("\n### Indicateurs de Complexité\n\n")
            for indicator, data in insights['complexity_indicators'].items():
                f.write(f"- **{indicator.replace('_', ' ').title()}**: {'✓' if data['indicator'] else '✗'}\n")
                f.write(f"  - {data['description']}\n")
            
            # Recommandations
            f.write("\n## Recommandations\n\n")
            recommendations = report['recommendations']
            
            f.write("### Pour l'Entraînement de Modèles\n\n")
            for rec in recommendations['for_model_training']:
                f.write(f"- {rec}\n")
            
            f.write("\n### Pour la Résolution de Puzzles\n\n")
            for rec in recommendations['for_puzzle_solving']:
                f.write(f"- {rec}\n")

def main():
    """Fonction principale."""
    correlation_file = "src/grid_analysis/correlation_analysis/feature_scenario_correlations.json"
    output_dir = "src/grid_analysis/synthesis_results"
    
    if not Path(correlation_file).exists():
        logger.error(f"Fichier de corrélations non trouvé: {correlation_file}")
        return
    
    synthesizer = CorrelationSynthesizer(correlation_file)
    synthesizer.load_correlations()
    synthesizer.save_synthesis(output_dir)
    
    logger.info("Synthèse terminée avec succès!")

if __name__ == "__main__":
    main()